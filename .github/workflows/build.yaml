name: Build and Publish

on:
  push:
    paths-ignore:
      - "**/CODEOWNERS"
      - "**/*.md"
      - "**/*.yaml"
      - "**/*.yml"
      - "**/.gitignore"
  
jobs:
  build-and-publish:
    name: Build and Publish Application
    uses: AAInternal/itfacs-payments-workflows/.github/workflows/ru_maven.yml@cloudsmith
    secrets: inherit
    permissions:
      id-token: write
      pull-requests: write
      contents: write
      issues: write
      security-events: write
      actions: read
    with:
      javaVersion: 23
      usesParentPom: true