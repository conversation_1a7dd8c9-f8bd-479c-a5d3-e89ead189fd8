package com.aa.findatahub.redis;

import java.util.ArrayList;
import java.util.List;

import com.aa.findatahub.redis.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import com.aa.findatahub.redis.controller.RedisController;
import com.aa.findatahub.redis.exception.NoDataFoundException;
import com.aa.findatahub.redis.service.RedisService;

import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class RedisControllerTests {

    @Mock
    private RedisService redisService;


    @InjectMocks
    private RedisController controller;


    @Test
    public void checkMinorUnit(){
        PaymentCurrencyUnit currencyUnit=new PaymentCurrencyUnit("usd","dollar dollar billz",2);

        Mockito.when(redisService.getValue("PaymentCurrencyUnit","usd"))
                .thenReturn((Mono) Mono.just(currencyUnit));

        StepVerifier.create(controller.getMinorUnit("usd"))
                .expectNext(2)
                .verifyComplete();
    }


    @Test
    public void checkPosEntityCurrency(){
        PosEntityCurrency posEntityCurrencyAED = new PosEntityCurrency("AE","AED","784");
        PosEntityCurrency posEntityCurrencyUSD = new PosEntityCurrency("AE","USD","840");
        List<PosEntityCurrency> posEntityCurrencies = new ArrayList<>();
        posEntityCurrencies.add(posEntityCurrencyUSD);
        posEntityCurrencies.add(posEntityCurrencyAED);

        Mockito.when(redisService.getZSets("PosEntityCurrency","AE"))
                .thenReturn((Mono) Mono.just(posEntityCurrencies));

        StepVerifier.create(controller.getPosEntityCurrencies("AE"))
                .expectNext(posEntityCurrencies)
                .verifyComplete();
    }

    @Test
    public void checkPosEntityCurrencyError(){

        Mockito.when(redisService.getZSets("PosEntityCurrency","CAN"))
                .thenReturn( Mono.error(new NoDataFoundException("PosEntityCurrency")));

        StepVerifier.create(controller.getPosEntityCurrencies("CAN"))
                .expectError(NoDataFoundException.class)
                .verify();
    }


    @Test
    public void checkMinorUnitError(){
        Mockito.when(redisService.getValue("PaymentCurrencyUnit","munny"))
                .thenReturn( Mono.error(new NoDataFoundException("payment")));

        StepVerifier.create(controller.getMinorUnit("munny"))
                .expectError(NoDataFoundException.class)
                .verify();
    }


    @Test
    public void checkProcessorCode(){
        ProcessorCode processorCode= new ProcessorCode("Amex","AX");

        Mockito.when(redisService.getValue("ProcessorCode","Amex"))
                .thenReturn((Mono) Mono.just(processorCode));

        StepVerifier.create(controller.getProcessorCode("Amex"))
                .expectNext("AX")
                .verifyComplete();

    }

    @Test
    public void checkProcessorCodeError()
    {
        Mockito.when(redisService.getValue("ProcessorCode","foo"))
                .thenReturn( Mono.error(new NoDataFoundException("payment")));

        StepVerifier.create(controller.getProcessorCode("foo"))
                .expectError(NoDataFoundException.class)
                .verify();
    }

    @Test
    public void getCurrencyCode() {
        CurrencyKey currencyKey = new CurrencyKey("USD");
        CurrencyCode currencyCode = new CurrencyCode(2, "USD", "US");
        Mockito.when(redisService.getValue("CurrencyCode",currencyKey))
                .thenReturn((Mono) Mono.just(currencyCode));

        StepVerifier.create(controller.getCurrencyCode(currencyKey))
                .expectNext(currencyCode)
                .verifyComplete();
    }
    
    @Test 
    public void giveProcessorLookup_whenGetProcessorIsCalled_thenReturnProcessor() {
    	ProcessorKey processorLookup = new ProcessorKey("AX", "AE", "AED", "BSP", "SALE");
    	Processor processor = new Processor("AX", "AE", "AED", "BSP", "SALE", "AX", "9590730313", "AX202BBSP");
    	
    	Mockito.when(redisService.getValue("processor", processorLookup)).thenReturn((Mono) Mono.just(processor));
    	
    	StepVerifier.create(controller.getProcessor(processorLookup))
    			.expectNext(processor)
    			.verifyComplete();
    	
    	Mockito.verify(redisService, Mockito.times(1)).getValue("processor", processorLookup);
    }


    @Test 
    public void giveProcessorLookup_whenGetProcessorIsCalled_thenThrowNotFoundException() {
    	ProcessorKey processorLookup = new ProcessorKey("AX", "AE", "AED", "BSP", "SALE");
    	    	
    	Mockito.when(redisService.getValue("processor", processorLookup))
    			.thenReturn(Mono.error(new NoDataFoundException("processorLookup")));
    	
    	StepVerifier.create(controller.getProcessor(processorLookup))
    			.expectError(NoDataFoundException.class)
    			.verify();
    	
    	Mockito.verify(redisService, Mockito.times(1)).getValue("processor", processorLookup);
    }
    
}
