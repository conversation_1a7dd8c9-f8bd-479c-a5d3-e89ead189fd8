package com.aa.findatahub.redis.service;

import com.aa.findatahub.redis.entity.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.ReactiveRedisTemplate;

import com.aa.findatahub.redis.ComponentTest;
import com.aa.findatahub.redis.controller.RedisController;
import com.aa.findatahub.redis.exception.NoDataFoundException;

import static org.junit.jupiter.api.Assertions.fail;

import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.ArrayList;
import java.util.List;

@ExtendWith(value = {MockitoExtension.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class RedisServiceTests extends ComponentTest {

    @Autowired
    RedisController controller;

    @Autowired
    ReactiveRedisTemplateFactory factory;

    @Autowired
    @Qualifier("gatewayPaymentType")
    ReactiveRedisTemplate gatewayPaymentTypeTemplate;

    @Autowired
    @Qualifier("posEntity")
    ReactiveRedisTemplate posEntityTemplate;

    private final String GATEWAY_PAYMENT_TYPE_KEY = "visa";
    private final String PAYMENT_CURRENCY_UNIT_KEY = "usd";
    private final String POS_ENTITY_KEY = "45105174";
    private final String POS_ENTITY_CURRENCY_KEY = "AE";

    private final String PROCESSOR_NAME = "Amex";

    @Autowired
    @Qualifier("paymentCurrencyUnit")
    ReactiveRedisTemplate paymentCurrencyUnitTemplate;

    @Autowired
    @Qualifier("processorCode")
    ReactiveRedisTemplate processorCodeTemplate;

    @Autowired
    @Qualifier("posEntityCurrency")
    ReactiveRedisTemplate posEntityCurrencyTemplate;

    @Test
    public void getPosEntityCurrency() {
        PosEntityCurrency posEntityCurrencyAED = new PosEntityCurrency("AE","AED","784");
        PosEntityCurrency posEntityCurrencyUSD = new PosEntityCurrency("AE","USD","840");
        List<PosEntityCurrency> posEntityCurrencies = new ArrayList<>();
        posEntityCurrencies.add(posEntityCurrencyUSD);
        posEntityCurrencies.add(posEntityCurrencyAED);

        posEntityCurrencyTemplate.opsForZSet().add("AE",posEntityCurrencyAED,1).block();
        posEntityCurrencyTemplate.opsForZSet().add("AE",posEntityCurrencyUSD,2).block();

        Mono<List<PosEntityCurrency>> posEntityCurrencyProducer = (Mono<List<PosEntityCurrency>>)controller.getZSets("posEntityCurrency","AE");

        StepVerifier.create(posEntityCurrencyProducer)
                .assertNext(posEntityCurrenciesFromMono -> posEntityCurrenciesFromMono.containsAll(posEntityCurrencies))
                .verifyComplete();
    }
    @Test
    public void posEntityCurrency_NoDataFoundException() {
        Mono<List<PosEntityCurrency>> currencyProducer = (Mono<List<PosEntityCurrency>>) controller.getZSets("posEntityCurrency", "CHN");

        StepVerifier.create(currencyProducer)
                .expectError(NoDataFoundException.class)
                .verify();
    }

    @Test
    public void getGatewayPaymentType() {
        GatewayPaymentType gatewayPaymentType = new GatewayPaymentType("visa", "VI");
        boolean isPersisted = (boolean) gatewayPaymentTypeTemplate.opsForValue().set(GATEWAY_PAYMENT_TYPE_KEY, gatewayPaymentType).block();

        if (isPersisted) {
            Mono<GatewayPaymentType> gatewayPaymentTypeProducer = (Mono<GatewayPaymentType>) controller.getValue("GatewayPaymentType", GATEWAY_PAYMENT_TYPE_KEY);

            StepVerifier.create(gatewayPaymentTypeProducer)
                    .expectNext(gatewayPaymentType)
                    .verifyComplete();
        } else fail();
    }

    @Test
    public void gatewayPaymentType_NoDataFoundException() {
        gatewayPaymentTypeTemplate.opsForValue().delete(GATEWAY_PAYMENT_TYPE_KEY);
        Mono<GatewayPaymentType> paymentTypeProducer = (Mono<GatewayPaymentType>) controller.getValue("GatewayPaymentType", GATEWAY_PAYMENT_TYPE_KEY);
        
        StepVerifier.create(paymentTypeProducer)
                .expectError(NoDataFoundException.class)
                .verify();
    }

    @Test
    public void getPaymentCurrencyUnit() {
        PaymentCurrencyUnit paymentType = new PaymentCurrencyUnit("usd", "united states dollar",2);
        boolean isPersisted = (boolean) paymentCurrencyUnitTemplate.opsForValue().set(PAYMENT_CURRENCY_UNIT_KEY, paymentType).block();

        if (isPersisted) {
            Mono<PaymentCurrencyUnit> paymentTypeProducer = (Mono<PaymentCurrencyUnit>) controller.getValue("PaymentCurrencyUnit", PAYMENT_CURRENCY_UNIT_KEY);

            StepVerifier.create(paymentTypeProducer)
                    .expectNext(paymentType)
                    .verifyComplete();
        } else fail();
    }

    @Test
    public void paymentCurrencyUnit_NoDataFoundException() {
        paymentCurrencyUnitTemplate.opsForValue().delete(PAYMENT_CURRENCY_UNIT_KEY).block();
        Mono<PaymentCurrencyUnit> currencyProducer = (Mono<PaymentCurrencyUnit>) controller.getValue("PaymentCurrencyUnit", PAYMENT_CURRENCY_UNIT_KEY);

        StepVerifier.create(currencyProducer)
                .expectError(NoDataFoundException.class)
                .verify();
    }

    @Test
    public void getPosEntity() {
        PosEntity posEntity = new PosEntity("45105174", "DALLAS", "TX", "75261",
                "USA", "US", "USD", "840", 2,
                "WEB", "TX", "N", "WEB", "BC");
        boolean isPersisted = (boolean) posEntityTemplate.opsForValue().set(POS_ENTITY_KEY, posEntity).block();

        if(isPersisted) {
            Mono<PosEntity> posEntityProducer = (Mono<PosEntity>) controller.getValue("PosEntity", POS_ENTITY_KEY);

            StepVerifier.create(posEntityProducer)
                    .expectNext(posEntity)
                    .verifyComplete();
        } else fail();
    }

    @Test
    public void posEntity_NoDataFoundException() {
        posEntityTemplate.opsForValue().delete(POS_ENTITY_KEY).block();
        Mono<PosEntity> posEntityProducer = (Mono<PosEntity>) controller.getValue("PosEntity", POS_ENTITY_KEY);
        
        StepVerifier.create(posEntityProducer)
                .expectError(NoDataFoundException.class)
                .verify();
    }

    @Test
    public void getProcessorCode() {
        ProcessorCode processorCode = new ProcessorCode("Amex", "AX");
        boolean isPersisted = (boolean) processorCodeTemplate.opsForValue().set(PROCESSOR_NAME, processorCode).block();

        if (isPersisted) {
            Mono<ProcessorCode> processorCodeProducer = (Mono<ProcessorCode>) controller.getValue("ProcessorCode", PROCESSOR_NAME);

            StepVerifier.create(processorCodeProducer)
                    .expectNext(processorCode)
                    .verifyComplete();
        } else fail();
    }
    
    @Test
    public void processorCode_NoDataFoundException(){
//        processorCodeTemplate.opsForValue().delete(PROCESSOR_NAME).block();
        Mono<ProcessorCode> processorCodeProducer = (Mono<ProcessorCode>) controller.getValue("ProcessorCode", PROCESSOR_NAME);
        
        StepVerifier.create(processorCodeProducer)
                .expectError(NoDataFoundException.class)
                .verify();
    }

}
