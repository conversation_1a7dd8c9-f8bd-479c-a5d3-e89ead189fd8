package com.aa.findatahub.redis.service;

import com.aa.findatahub.redis.ComponentTest;
import com.aa.findatahub.redis.controller.RedisController;
import com.aa.findatahub.redis.entity.AriReceivableAccounting;
import com.aa.findatahub.redis.entity.AriReceivableAccountingKey;
import com.aa.findatahub.redis.exception.NoDataFoundException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.serializer.SerializationException;
import reactor.test.StepVerifier;

import java.util.AbstractMap;
import java.util.List;

@ExtendWith(value = {MockitoExtension.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class AriReceivableAccountingTests extends ComponentTest {

    @Autowired
    RedisController redisController;

    @Autowired
    ReactiveRedisTemplate<AriReceivableAccountingKey, ? super Object> ariReceivableAccounting;

    List<AbstractMap.SimpleEntry<AriReceivableAccountingKey, AriReceivableAccounting>> validEntries;
    List<AbstractMap.SimpleEntry<AriReceivableAccountingKey, ?>> invalidEntries;


    @BeforeAll
    public void setup() {
        loadCache();
    }

    @Test
    public void givenValidAccountingReferenceLookups_whenControllerMethodCalled_ThenVerifyReturnedCacheItems() {
        for(AbstractMap.SimpleEntry<AriReceivableAccountingKey, AriReceivableAccounting> entry : validEntries) {
            StepVerifier.create(redisController.getAccountingReference(entry.getKey()))
                    .expectNext(entry.getValue())
                    .verifyComplete();
        }
    }

    @Test
    public void givenValidAccountingReferenceLookups_whenControllerGetValueMethodCalled_ThenVerifyReturnedCacheItems() {
        for(AbstractMap.SimpleEntry<AriReceivableAccountingKey, AriReceivableAccounting> entry : validEntries) {
            StepVerifier.create(redisController.getValue(AriReceivableAccounting.class.getSimpleName(), entry.getKey()).cast(AriReceivableAccounting.class))
                    .expectNext(entry.getValue())
                    .verifyComplete();
        }
    }

    @Test
    public void givenInvalidAccountingReferenceLookup_whenPulledFromRedis_ThenVerifyNoDataReturnedError() {
        AriReceivableAccountingKey lookup = new AriReceivableAccountingKey("indicator");

        StepVerifier.create(redisController.getAccountingReference(lookup))
                .expectError(NoDataFoundException.class)
                .verify();
    }

    @Test
    public void givenNullAccountingReferenceLookup_whenPulledFromRedis_ThenVerifyIllegalArgumentError() {
        StepVerifier.create(redisController.getAccountingReference(null))
                .expectError(IllegalArgumentException.class)
                .verify();
    }

    @Test
    public void givenValidAccountingReferenceLookup_whenInvalidDataPulledFromRedis_ThenVerifySerializationError() {
        for(AbstractMap.SimpleEntry<AriReceivableAccountingKey, ?> entry : invalidEntries) {
            StepVerifier.create(redisController.getAccountingReference(entry.getKey()))
                    .expectError(NoDataFoundException.class)
                    .verify();
        }
    }

    private void loadCache() {
        validEntries = List.of(
                new AbstractMap.SimpleEntry<>(new AriReceivableAccountingKey("AX202BBSP"), new AriReceivableAccounting("AX202BBSP", "1240119", "1240152", "AA00","**********", "**********","C2 Sales Billing")),
                new AbstractMap.SimpleEntry<>(new AriReceivableAccountingKey("DS209BBSP"), new AriReceivableAccounting("DS209BBSP", "1240121", "1240152", "AA00" ,"**********", "**********", "C2 Sales Billing"))
        );

        for (AbstractMap.SimpleEntry<AriReceivableAccountingKey, AriReceivableAccounting> entry : validEntries) {
            ariReceivableAccounting.opsForValue().set(entry.getKey(), entry.getValue()).block();
        }

        invalidEntries = List.of(
                new AbstractMap.SimpleEntry<>(new AriReceivableAccountingKey("EL203BBSP"), "{}"),
                new AbstractMap.SimpleEntry<>(new AriReceivableAccountingKey("ZY204BBSP"), new AriReceivableAccountingKey())
        );
    }
}