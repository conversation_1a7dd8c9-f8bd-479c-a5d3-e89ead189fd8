package com.aa.findatahub.redis.service;

import static org.junit.Assert.assertThrows;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.ReactiveRedisTemplate;

import com.aa.findatahub.redis.ComponentTest;
import com.aa.findatahub.redis.controller.RedisController;
import com.aa.findatahub.redis.entity.Processor;
import com.aa.findatahub.redis.entity.ProcessorKey;
import com.aa.findatahub.redis.exception.NoDataFoundException;

import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(value = { MockitoExtension.class })
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class ProcessorTests extends ComponentTest {

	@Autowired
	RedisController controller;

	@Autowired
	@Qualifier("processor")
	ReactiveRedisTemplate processorReactiveRedisTemplate;

	@Test
	public void givenProcessorLookup_whenGetProcessorIsCalledAndCacheDataExists_ThenReturnCacheData() {
		Mono<Processor> processor = controller.getProcessor(getProcessorLookup());
		StepVerifier.create(processor).expectNext(getCacheData()).verifyComplete();
	}

	@Test
	public void givenProcessorLookup_whenGetProcessorIsCalledAndNoCacheDataExists_ThenThrowNoDataFoundException() {
		ProcessorKey lookUp = getProcessorLookup();
		lookUp.setCountryCode("USD");
		assertThrows(NoDataFoundException.class, () -> controller.getProcessor(lookUp).block());
	}

	@BeforeAll
	public void loadCache() {
		processorReactiveRedisTemplate.opsForValue().set(getProcessorLookup(), getCacheData()).block();

	}

	@AfterAll
	public void clearCache() {
		processorReactiveRedisTemplate.opsForValue().delete(getProcessorLookup()).block();
	}

	private Processor getCacheData() {
		Processor processorCacheData = new Processor("AX", "AE", "AED", "BSP", "SALE", "AX", "9590730313", "AX202BBSP");
		return processorCacheData;
	}

	private ProcessorKey getProcessorLookup() {
		ProcessorKey processorLookup = new ProcessorKey("AX", "AE", "AED", "BSP", "SALE");
		return processorLookup;
	}
}
