package com.aa.findatahub.redis.service;

import com.aa.findatahub.redis.ComponentTest;
import com.aa.findatahub.redis.controller.RedisController;
import com.aa.findatahub.redis.entity.LocalBillingStation;
import com.aa.findatahub.redis.entity.LocalBillingStationKey;
import com.aa.findatahub.redis.exception.DuplicateFoundException;
import com.aa.findatahub.redis.exception.NoDataFoundException;
import com.aa.findatahub.redis.lookup.LocalBillingStationLookup;
import com.aa.findatahub.redis.rule.localbillingstation.DateRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.system.CapturedOutput;
import org.springframework.boot.test.system.OutputCaptureExtension;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.testcontainers.shaded.com.google.common.collect.HashMultimap;

import java.time.LocalDate;

import static org.assertj.core.api.Fail.fail;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;


@ExtendWith(value = {MockitoExtension.class, OutputCaptureExtension.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@Slf4j
public class LocalBillingStationTest extends ComponentTest {

    @Autowired
    RedisController redisController;

    @Autowired
    ReactiveRedisTemplate<String, Object> localBillingStation;

    HashMultimap<LocalBillingStationKey, LocalBillingStation> validEntries = HashMultimap.create();
    HashMultimap<LocalBillingStationKey, ? super Object> invalidEntries = HashMultimap.create();

    @BeforeEach
    public void setup() {
        localBillingStation
                .getConnectionFactory()
                .getReactiveConnection()
                .serverCommands()
                .flushAll()
                .block();
    }

    /***
     *
     */
    @ParameterizedTest
    @CsvSource({
            "2024,6,15,1",
            "2024,6,1,1",
            "2024,9,1,1",
            "2024,5,31,0",
            "2020,1,15,0",
            "2025,1,1,0",
    })
    public void givenLocalBilling_WhenEffectiveDateInAndOutOfRange_ThenVerifySingleReturnOrNoDataFoundError(int year, int month, int day, int numReturns, CapturedOutput output) {
        loadCache(false);
        LocalBillingStationLookup localBillingStationLookup =
            new LocalBillingStationLookup("SALE", "55101104", "AX", "AR", "ARS",
                    LocalDate.of(year, month, day), LocalDate.of(year, month, day));

        switch (numReturns) {
            case 0 -> {
                assertThrows(NoDataFoundException.class, () -> redisController.getLocalBillingStation(localBillingStationLookup).block());
                assertTrue(output.getOut().contains("No value found that applies to rule " + DateRule.class.getSimpleName()));
            }
            case 1 -> {
                LocalBillingStation localBillingStation = redisController.getLocalBillingStation(localBillingStationLookup).block();
                assertNotNull(localBillingStation);
                assertTrue(output.getOut().contains("Found exactly one value that applies to rule " + DateRule.class.getSimpleName()));
            }
            default -> fail("Unexpected number of returns: " + numReturns);
        }
    }

    /***
     *
     */
    @ParameterizedTest
    @CsvSource({
            "2024,8,2"
    })
    public void givenLocalBilling_WhenEffectiveDateInRangeWithOverlapping_ThenVerifyNoDataFoundError(int year, int month, int day, CapturedOutput output) {
        loadCache(true);
        LocalBillingStationLookup localBillingStationLookup =
            new LocalBillingStationLookup("SALE", "55101104", "AX", "AR", "ARS",
                    LocalDate.of(year, month, day), LocalDate.of(year, month, day));

        assertThrows(DuplicateFoundException.class, () -> redisController.getLocalBillingStation(localBillingStationLookup).block());
        assertTrue(output.getOut().contains("Found more than one value that applies to rule " + DateRule.class.getSimpleName()));
    }

    /***
     *
     */
    @ParameterizedTest
    @CsvSource({
            "2024,8,2"
    })
    public void givenLocalBilling_WhenDataNotFoundInRedis_ThenVerifyNoDataFoundError(int year, int month, int day, CapturedOutput output) {
        loadCache(true);
        LocalBillingStationLookup localBillingStationLookup =
                new LocalBillingStationLookup("SALE", "55101104", "AX", "AR", "ABC",
                        LocalDate.of(year, month, day), LocalDate.of(year, month, day));

        assertThrows(NoDataFoundException.class, () -> redisController.getLocalBillingStation(localBillingStationLookup).block());
        assertTrue(output.getOut().contains("Error applying rules"));
    }

    /***
     *
     */
    @ParameterizedTest
    @CsvSource({
            "2024,8,2"
    })
    public void givenLocalBilling_WhenInvalidDataFoundInRedis_ThenVerifyErrorConnectingToCache(int year, int month, int day, CapturedOutput output) {
        loadCache(true);
        LocalBillingStationLookup localBillingStationLookup =
                new LocalBillingStationLookup("SALE", "********", "AX", "AR", "ARS",
                        LocalDate.of(year, month, day), LocalDate.of(year, month, day));

        assertThrows(RedisConnectionFailureException.class, () -> redisController.getLocalBillingStation(localBillingStationLookup).block());
        assertTrue(output.getOut().contains("Error applying rules"));
    }

    /***
     *
     */
    private void loadCache(boolean overlap) {
        addToMap(
                validEntries,
                "SALE",
                "55101104",
                "AX",
                "AR",
                "ARS",
                false,
                false,
                "123",
                "",
                "AX202LCL",
                LocalDate.of(2024, 6, 1),
                LocalDate.of(2024, 9, 1)
        );

        addToMap(
                validEntries,
                "SALE",
                "55101104",
                "MC",
                "AR",
                "ARS",
                false,
                false,
                "EL",
                "",
                "MC209LCL",
                LocalDate.of(2024, 1, 1),
                LocalDate.of(2024, 5, 31)
        );

        if (overlap) {
            addToMap(
                    validEntries,
                    "SALE",
                    "55101104",
                    "AX",
                    "AR",
                    "ARS",
                    false,
                    false,
                    "123",
                    "",
                    "AX202LCL",
                    LocalDate.of(2024, 8, 2),
                    LocalDate.of(2999, 12, 31)
            );
        }

        invalidEntries.put(new LocalBillingStationKey("SALE", "********", "AX", "AR", "ARS"), "{}");
        invalidEntries.forEach((key, value) -> {
            localBillingStation.opsForZSet().add(key.toString(), value, 0).block();
        });
    }

    private void addToMap(HashMultimap<LocalBillingStationKey, LocalBillingStation> map, String transactionType,
                          String stationNumber, String paymentType, String countryCode, String currency, boolean cps, boolean vivaldi,
                          String processorId, String merchantId, String accountingReferenceIndicator, LocalDate effectiveStartDate, LocalDate effectiveEndDate) {
        LocalBillingStationKey key = new LocalBillingStationKey(
                transactionType,
                stationNumber,
                paymentType,
                countryCode,
                currency
        );
        LocalBillingStation value = new LocalBillingStation(
                transactionType,
                stationNumber,
                paymentType,
                countryCode,
                currency,
                cps,
                vivaldi,
                processorId,
                merchantId,
                accountingReferenceIndicator,
                effectiveStartDate,
                effectiveEndDate
        );
        map.put(key, value);
        localBillingStation.opsForZSet().add(key.toString(), value, 0).block();
    }

}
