package com.aa.findatahub.redis.service;

import com.aa.findatahub.redis.entity.PosEntityCurrency;
import com.aa.findatahub.redis.exception.NoDataFoundException;
import com.aa.findatahub.redis.rule.RuleExecutor;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveZSetOperations;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(value = {MockitoExtension.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringBootTest(classes = {RedisService.class})
public class RetryCacheZSetTest {

    @Autowired
    @InjectMocks
     RedisService redisService;

    @MockitoBean
    ReactiveRedisTemplateFactory factory;


    @MockitoBean
    @Qualifier("posEntityCurrency")
    ReactiveRedisTemplate paymentRedisTemplate;
    @MockitoBean
    ReactiveZSetOperations reactiveZSetOperations;

    @MockitoBean
    RuleExecutor ruleExecutor;

    @Test
    @DisplayName("Happy Path: No Error")
    public void given_NoRedisConnectionError_When_connectionToCacheIsSuccessful_Then_ReturnObjectZSet(){
        PosEntityCurrency posEntityCurrencyAED = new PosEntityCurrency("AE","AED","784");
        PosEntityCurrency posEntityCurrencyUSD = new PosEntityCurrency("AE","USD","840");
        List<PosEntityCurrency> posEntityCurrencies = new ArrayList<>();
        posEntityCurrencies.add(posEntityCurrencyUSD);
        posEntityCurrencies.add(posEntityCurrencyAED);
        when(factory.getRedisTemplate(any())).thenReturn(paymentRedisTemplate);

        when(paymentRedisTemplate.opsForZSet()).thenReturn(reactiveZSetOperations);
        when(reactiveZSetOperations.range(anyString(),any()))
                .thenReturn(Flux.just(posEntityCurrencies));

        Mono<List<PosEntityCurrency>> posEntityCurrencyProducer = (Mono<List<PosEntityCurrency>>) redisService.getZSets("posEntityCurrency","AE");

        StepVerifier.create(posEntityCurrencyProducer)
                .assertNext(posEntityCurrenciesFromMono -> posEntityCurrenciesFromMono.containsAll(posEntityCurrencies))
                .verifyComplete();

        Mockito.verify(paymentRedisTemplate,Mockito.times(1)).opsForZSet();

    }
    @Test
    @DisplayName("Unhappy Path: Error + retries exhausted")
    public void given_redisConnectionError_When_retriesIsExhausted_Then_throwReddisConnectionErrorZSet(){

        when(factory.getRedisTemplate(any())).thenReturn(paymentRedisTemplate);

        when(paymentRedisTemplate.opsForZSet()).thenReturn(reactiveZSetOperations);
        when(reactiveZSetOperations.range(anyString(),any()))
                .thenReturn(Flux.error(()->new RedisConnectionFailureException("Failed to Connect")));

        Mono<List<PosEntityCurrency>> posEntityCurrencyProducer = (Mono<List<PosEntityCurrency>>) redisService.getZSets("posEntityCurrency","AE");

        StepVerifier.create(posEntityCurrencyProducer).
        expectError(RedisConnectionFailureException.class)
                .verify();

        Mockito.verify(paymentRedisTemplate,Mockito.times(3)).opsForZSet();

    }

    @Test
    @DisplayName("Alternate Path: Empty Object")
    public void given_emptyValue_When_callingTheCache_Then_throwNoDataFoundExceptionZSet(){
        when(factory.getRedisTemplate(any())).thenReturn(paymentRedisTemplate);

        when(paymentRedisTemplate.opsForZSet()).thenReturn(reactiveZSetOperations);
        when(reactiveZSetOperations.range(anyString(),any()))
                .thenReturn(Flux.empty());

        Mono<List<PosEntityCurrency>> posEntityCurrencyProducer = (Mono<List<PosEntityCurrency>>) redisService.getZSets("posEntityCurrency","AE");

        StepVerifier.create(posEntityCurrencyProducer).
                expectError(NoDataFoundException.class)
                .verify();

        Mockito.verify(paymentRedisTemplate,Mockito.times(1)).opsForZSet();
    }

    @Test
    @DisplayName("Alternate Path: Error first attempt, successful second attempt")
    public void given_redisConnectionSuccessful_WhenRetrying_Then_ReturnObjectAndNoErrorThrown(){
        PosEntityCurrency posEntityCurrencyAED = new PosEntityCurrency("AE","AED","784");
        PosEntityCurrency posEntityCurrencyUSD = new PosEntityCurrency("AE","USD","840");
        List<PosEntityCurrency> posEntityCurrencies = new ArrayList<>();
        posEntityCurrencies.add(posEntityCurrencyUSD);
        posEntityCurrencies.add(posEntityCurrencyAED);

        when(factory.getRedisTemplate(any())).thenReturn(paymentRedisTemplate);

        when(paymentRedisTemplate.opsForZSet()).thenReturn(reactiveZSetOperations);
        when(reactiveZSetOperations.range(anyString(),any()))
                .thenReturn(Flux.error(()->new RedisConnectionFailureException("Failed to Connect")))
                .thenReturn(Flux.just(posEntityCurrencies));

        Mono<List<PosEntityCurrency>> posEntityCurrencyProducer = (Mono<List<PosEntityCurrency>>) redisService.getZSets("posEntityCurrency","AE");

        StepVerifier.create(posEntityCurrencyProducer)
                .assertNext(posEntityCurrenciesFromMono -> posEntityCurrenciesFromMono.containsAll(posEntityCurrencies))
                .verifyComplete();

        Mockito.verify(paymentRedisTemplate,Mockito.times(2)).opsForZSet();
    }

}

