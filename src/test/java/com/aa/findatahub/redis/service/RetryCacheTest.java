package com.aa.findatahub.redis.service;

import com.aa.findatahub.redis.entity.PaymentCurrencyUnit;
import com.aa.findatahub.redis.exception.NoDataFoundException;
import com.aa.findatahub.redis.rule.RuleExecutor;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(value = {MockitoExtension.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringBootTest(classes = {RedisService.class})
public class RetryCacheTest {

    @Autowired
    @InjectMocks
    RedisService redisService;

    @MockitoBean
    ReactiveRedisTemplateFactory factory;

    @Qualifier("paymentCurrencyUnit")
    @MockitoBean
    ReactiveRedisTemplate paymentRedisTemplate;
    @MockitoBean
    ReactiveValueOperations reactiveValueOperations;

    @MockitoBean
    RuleExecutor ruleExecutor;

    @Test
    @DisplayName("Happy Path: No Error")
    public void given_NoRedisConnectionError_When_connectionToCacheIsSuccessful_Then_ReturnObject(){

        PaymentCurrencyUnit currencyUnit=new PaymentCurrencyUnit("usd","dollar dollar billz",2);

        Mockito.when(factory.getRedisTemplate(any())).thenReturn(paymentRedisTemplate);

        Mockito.when(paymentRedisTemplate.opsForValue()).thenReturn(reactiveValueOperations);
        Mockito.when(reactiveValueOperations.get("US"))
                .thenReturn(Mono.just(currencyUnit));

        Mono<PaymentCurrencyUnit> paymentTypeProducer = (Mono<PaymentCurrencyUnit>) redisService.getValue("paymentCurrencyUnit", "US");

        StepVerifier.create(paymentTypeProducer)
                .expectNext(currencyUnit)
                .verifyComplete();

        Mockito.verify(reactiveValueOperations,Mockito.times(1)).get(anyString());


    }
    @Test
    @DisplayName("Unhappy Path: Error + retries exhausted")
    public void given_redisConnectionError_When_retriesIsExhausted_Then_throwReddisConnectionError(){

        Mockito.when(factory.getRedisTemplate(any())).thenReturn(paymentRedisTemplate);
        Mockito.when(paymentRedisTemplate.opsForValue()).thenReturn(reactiveValueOperations);
        Mockito.when(reactiveValueOperations.get("US")).thenReturn(Mono.error(()->new RedisConnectionFailureException("Failed to Connect")));

        Mono<Error> paymentTypeProducer = (Mono<Error>) redisService.getValue("paymentCurrencyUnit", "US");

        StepVerifier.create(paymentTypeProducer)
                .expectError(RedisConnectionFailureException.class)
                .verify();


        Mockito.verify(reactiveValueOperations,Mockito.times(3)).get(anyString());

    }

    @Test
    @DisplayName("Alternate Path: Empty Object")
    public void given_emptyValue_When_callingTheCache_Then_throwNoDataFoundException(){

        Mockito.when(factory.getRedisTemplate(any())).thenReturn(paymentRedisTemplate);

        Mockito.when(paymentRedisTemplate.opsForValue()).thenReturn(reactiveValueOperations);
        Mockito.when(reactiveValueOperations.get("US")).thenReturn(Mono.empty());

        Mono<Error> paymentTypeProducer = (Mono<Error>) redisService.getValue("paymentCurrencyUnit", "US");

        StepVerifier.create(paymentTypeProducer)
                .expectError(NoDataFoundException.class)
                .verify();

        Mockito.verify(reactiveValueOperations,Mockito.times(1)).get(anyString());

    }
    @Test
    @DisplayName("Alternate Path: Error first attempt, successful second attempt")
    public void given_redisConnectionSuccessful_WhenRetrying_Then_ReturnObjectAndNoErrorThrown(){

        PaymentCurrencyUnit currencyUnit=new PaymentCurrencyUnit("usd","dollar dollar billz",2);

        Mockito.when(factory.getRedisTemplate(any())).thenReturn(paymentRedisTemplate);

        Mockito.when(paymentRedisTemplate.opsForValue()).thenReturn(reactiveValueOperations);
        Mockito.when(reactiveValueOperations.get("US")).thenReturn(Mono.error(()->new RedisConnectionFailureException("Failed to Connect")))
                .thenReturn(Mono.just(currencyUnit));
        Mono<PaymentCurrencyUnit> paymentTypeProducer = (Mono<PaymentCurrencyUnit>) redisService.getValue("paymentCurrencyUnit", "US");

        StepVerifier.create(paymentTypeProducer)
                .expectNext(currencyUnit).verifyComplete();
        Mockito.verify(reactiveValueOperations,Mockito.times(2)).get(anyString());

    }

}


