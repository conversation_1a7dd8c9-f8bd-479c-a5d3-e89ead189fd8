package com.aa.findatahub.redis.service;

import com.aa.findatahub.redis.ComponentTest;
import com.aa.findatahub.redis.controller.RedisController;
import com.aa.findatahub.redis.entity.PaymentType;
import com.aa.findatahub.redis.entity.PaymentTypeLookup;
import com.aa.findatahub.redis.exception.NoDataFoundException;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.security.InvalidParameterException;

import static org.mockito.Mockito.times;

@ExtendWith(value = {MockitoExtension.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class PaymentTypeTest extends ComponentTest {
    @InjectMocks
    @Autowired
    RedisController controller;
    

    @Autowired
    @Qualifier("paymentType")
    ReactiveRedisTemplate paymentRedisTemplate;

    @MockitoSpyBean
    @Autowired
    RedisService redisService;

    @Test
    public void clientSendsOAUATTest() {
        PaymentType oaTP = new PaymentType("1987", null, "OA", "OA UATP", null, null,false);
        PaymentTypeLookup oaKey = new PaymentTypeLookup("1987", null);

        Mono<PaymentType> paymentTypeProducer = controller.getPaymentType("1987", "4444");

        StepVerifier.create(paymentTypeProducer)
                .expectNext(oaTP)
                .verifyComplete();

        Mockito.verify(redisService, times(1)).getValue("paymentType", oaKey);
    }

    @Test
    public void clientSendsAAUATPTest() {
        PaymentType aaTP =  new PaymentType("1001",null,"TP","AA UATP", null,null, false);
        PaymentTypeLookup tpKey = new PaymentTypeLookup("1001",null);
        Mono<PaymentType> paymentTypeProducer = controller.getPaymentType("100199", "77");
        StepVerifier.create(paymentTypeProducer)
                .expectNext(aaTP)
                .verifyComplete();
        Mockito.verify(redisService,times(1)).getValue("paymentType",tpKey);
    }
    @Test
    public void clientSendsMastercardTest() {
         PaymentType mastercard =  new PaymentType("27",null,"MC","Mastercard", 7,6,false);
        PaymentTypeLookup mcKey = new PaymentTypeLookup("27","");
        Mono<PaymentType> paymentTypeProducer = controller.getPaymentType("273333", "4444");
        StepVerifier.create(paymentTypeProducer)
                .expectNext(mastercard)
                .verifyComplete();

        Mockito.verify(redisService,times(1)).getValue("paymentType",mcKey);

    }
    @Test
    public void clientSendsMastercardTestWithXXXX() {
        PaymentType mastercard =  new PaymentType("27",null,"MC","Mastercard", 7,6,false);
        PaymentTypeLookup mcKey = new PaymentTypeLookup("27","");
        Mono<PaymentType> paymentTypeProducer = controller.getPaymentType("27XXXX", "4444");
        StepVerifier.create(paymentTypeProducer)
                .expectNext(mastercard)
                .verifyComplete();

        Mockito.verify(redisService,times(1)).getValue("paymentType",mcKey);

    }
    @Test
    public void clientSendsJCBTest() {
        PaymentType jc =  new PaymentType("35",null,"JC","JCB", null,null,false);
        PaymentTypeLookup jcKey = new PaymentTypeLookup("35",null);
        Mono<PaymentType> paymentTypeProducer = controller.getPaymentType("355555", null);
        StepVerifier.create(paymentTypeProducer)
                .expectNext(jc)
                .verifyComplete();
        Mockito.verify(redisService,times(1)).getValue("paymentType",jcKey);

    }

    @Test
    public void unionPayDiscoverTest() {
        PaymentType unionPay =  new PaymentType("62",null,"UP","Union Pay", null,null,false);
        PaymentTypeLookup unionPayKey = new PaymentTypeLookup("62","");

        PaymentType discover =  new PaymentType("6",null,"DS","Discover", 7,6,false);
        PaymentTypeLookup discoverKey = new PaymentTypeLookup("6","");

        Mono<PaymentType> paymentTypeProducerUP = controller.getPaymentType("625555", null);
        StepVerifier.create(paymentTypeProducerUP)
                .expectNext(unionPay)
                .verifyComplete();

        Mono<PaymentType> paymentTypeProducer = controller.getPaymentType("611111", null);
        StepVerifier.create(paymentTypeProducer)
                .expectNext(discover)
                .verifyComplete();

        Mockito.verify(redisService,times(1)).getValue("paymentType",unionPayKey);
        Mockito.verify(redisService,times(1)).getValue("paymentType",discoverKey);

    }
    @Test
    public void clientSendPMTest() {
        PaymentType posTerminal =  new PaymentType("75","xxxx","PM","Adyen & ICICI POS Terminals", null,null,false);
        PaymentTypeLookup key = new PaymentTypeLookup("75","xxxx");
        Mono<PaymentType> paymentTypeCapital = controller.getPaymentType("757575", "XXXX");
        Mono<PaymentType> paymentTypeLower = controller.getPaymentType("757575", "xxxx");

        StepVerifier.create(paymentTypeCapital)
                .expectNext(posTerminal)
                .verifyComplete();

        StepVerifier.create(paymentTypeLower)
                .expectNext(posTerminal)
                .verifyComplete();

        Mockito.verify(redisService,times(2)).getValue("paymentType",key);

    }

    @Test
    public void clientSendMilesFOPTest() {
        PaymentType milesFop =  new PaymentType("76",null,"ML","Miles as a FOP", null,null,false);
        PaymentTypeLookup key = new PaymentTypeLookup("76","");

        Mono<PaymentType> paymentTypeProducer = controller.getPaymentType("760101", "1234");

        StepVerifier.create(paymentTypeProducer)
                .expectNext(milesFop)
                .verifyComplete();
        Mockito.verify(redisService,times(1)).getValue("paymentType",key);
        Assert.assertFalse(paymentTypeProducer.block().isAmadeusafop());


    }

    @Test
    public void clientAffirmTest() {
        PaymentType affirm =  new PaymentType("77","0016","AM","Affirm", null,null,false);
        PaymentTypeLookup key = new PaymentTypeLookup("77","0016");
        Mono<PaymentType> paymentTypeProducer = controller.getPaymentType("773232", "0016");

        StepVerifier.create(paymentTypeProducer)
                .expectNext(affirm)
                .verifyComplete();
        Mockito.verify(redisService,times(1)).getValue("paymentType",key);
    }

    @Test
    public void wechatTest() {
        PaymentType weChat =  new PaymentType("71","2345","WC","WeChat Pay", 7,6,true);
        PaymentTypeLookup weChatKey = new PaymentTypeLookup("71","2345");
        Mono<PaymentType> paymentTypeProducer = controller.getPaymentType("71", "2345");

        StepVerifier.create(paymentTypeProducer)
                .expectNext(weChat)
                .verifyComplete();
        Mockito.verify(redisService,times(1)).getValue("paymentType",weChatKey);
        Assertions.assertTrue(paymentTypeProducer.block().isAmadeusafop());
    }

    @Test
    public void givenDataInvalidInRedisCache_thenThrowSerializationException() {
        PaymentTypeLookup key = new PaymentTypeLookup("36","");
        Mono<PaymentType> paymentTypeProducer = controller.getPaymentType("36102", "");

        StepVerifier.create(paymentTypeProducer)
                .expectError(SerializationException.class)
                .verify();
        Mockito.verify(redisService,times(1)).getValue("paymentType",key);

    }

    @Test
    public void clientsSendsEmptyBinThrowException() {
        Mono<PaymentType> paymentTypeProducer = controller.getPaymentType(null, "4444");
        StepVerifier.create(paymentTypeProducer)
                .expectError(InvalidParameterException.class)
                .verify();
    }

    @Test
    public void noPaymentTypeFoundThrowException() {
            Mono<PaymentType> paymentTypeProducer = controller.getPaymentType("888888", "0002");
            StepVerifier.create(paymentTypeProducer)
                    .expectError(NoDataFoundException.class)
                    .verify();

    }

    @BeforeAll
    public void loadCache(){
        PaymentTypeLookup weChatKey = new PaymentTypeLookup("71","2345");
        PaymentType weChat =  new PaymentType("71","2345","WC","WeChat Pay", 7,6,true);


        PaymentTypeLookup googlePayKey = new PaymentTypeLookup("77","0002");
        PaymentType googlePay =  new PaymentType("77","0002","AM","Google Pay", null,null,false);
        PaymentTypeLookup oaKey = new PaymentTypeLookup("1987",null);
        PaymentType oaTP =  new PaymentType("1987",null,"OA","OA UATP", null,null,false);
        PaymentTypeLookup tpKey = new PaymentTypeLookup("1001",null);
        PaymentType aaTP =  new PaymentType("1001",null,"TP","AA UATP", null,null,false);
        PaymentTypeLookup mcKey = new PaymentTypeLookup("27","");
        PaymentType mastercard =  new PaymentType("27",null,"MC","Mastercard", 7,6,false);
        PaymentTypeLookup jcKey = new PaymentTypeLookup("35",null);
        PaymentType jc =  new PaymentType("35",null,"JC","JCB", null,null,false);
        PaymentTypeLookup discoverKey = new PaymentTypeLookup("6","");
        PaymentType discover =  new PaymentType("6",null,"DS","Discover", 7,6,false);
        PaymentTypeLookup unionPayKey = new PaymentTypeLookup("62","");
        PaymentType unionPay =  new PaymentType("62",null,"UP","Union Pay", null,null,false);
        PaymentTypeLookup pmKey = new PaymentTypeLookup("75","xxxx");
        PaymentType posTerminal =  new PaymentType("75","xxxx","PM","Adyen & ICICI POS Terminals", null,null,false);
        PaymentTypeLookup mlKey = new PaymentTypeLookup("76","");
        PaymentType milesFop =  new PaymentType("76",null,"ML","Miles as a FOP", null,null,false);

        PaymentTypeLookup affirmKey = new PaymentTypeLookup("77","0016");
        PaymentType affirm =  new PaymentType("77","0016","AM","Affirm", null,null,false);


        paymentRedisTemplate.opsForValue().set(affirmKey, affirm).block();
        paymentRedisTemplate.opsForValue().set(mlKey, milesFop).block();
        paymentRedisTemplate.opsForValue().set(pmKey, posTerminal).block();
        paymentRedisTemplate.opsForValue().set(unionPayKey, unionPay).block();
        paymentRedisTemplate.opsForValue().set(discoverKey, discover).block();
        paymentRedisTemplate.opsForValue().set(jcKey, jc).block();
        paymentRedisTemplate.opsForValue().set(mcKey, mastercard).block();
        paymentRedisTemplate.opsForValue().set(tpKey, aaTP).block();
        paymentRedisTemplate.opsForValue().set(oaKey, oaTP).block();
        paymentRedisTemplate.opsForValue().set(googlePayKey, googlePay).block();
        paymentRedisTemplate.opsForValue().set(weChatKey, weChat).block();

        PaymentTypeLookup invalidKey = new PaymentTypeLookup("36","");
        paymentRedisTemplate.opsForValue().set(invalidKey, "{\"binPrefix\":\"36\",\"lastFourDigits\":\"\", paymentType\":\"DI\"}").block();


    }
}
