package com.aa.findatahub.redis.service;

import com.aa.findatahub.redis.ComponentTest;
import com.aa.findatahub.redis.controller.RedisController;
import com.aa.findatahub.redis.entity.NonSabreTicket;
import com.aa.findatahub.redis.entity.NonSabreTicketKey;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.ReactiveRedisTemplate;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class NonSabreTicketTests extends ComponentTest {
    @Autowired
    RedisController controller;

    @Autowired
    @Qualifier("nonSabreTicket")
    ReactiveRedisTemplate redisTemplate;


    private final static String ADYEN = "ADYEN";
    private final static String MERCHANT_ACCT = "MerchantAccount";
    private final static String AAWIFI = "AAWIFI";
    private final static String AAWEBSALES = "AAWEBSALES";
    private final static String AFFIRM = "AFFIRM";
    private final static String MID_RESULT = "**********";

    @BeforeAll
    void setup() {
        var adyenAawifiKey = new NonSabreTicketKey(ADYEN, AAWIFI);
        var adyenAawifi = new NonSabreTicket(ADYEN, MERCHANT_ACCT, AAWIFI);
        var adyenAawebsalesKey = new NonSabreTicketKey(ADYEN, AAWEBSALES);
        var adyenAawebsales = new NonSabreTicket(ADYEN, MERCHANT_ACCT, AAWEBSALES);
        var affirmKey = new NonSabreTicketKey(AFFIRM, MID_RESULT);
        var affirm = new NonSabreTicket(AFFIRM, "MID", AAWEBSALES);

        redisTemplate.opsForValue().set(adyenAawifiKey, adyenAawifi).block();
        redisTemplate.opsForValue().set(affirmKey, affirm).block();
        redisTemplate.opsForValue().set(adyenAawebsalesKey, adyenAawebsales).block();
    }

    @DisplayName("Given a processor and result, when the non sabre redis cache is queried, then return the non sabre ticket")
    @Test
    void testGetNonSabreTicket_Success() {
        var result = controller.getNonSabreTicket("adyen", "aawifi").block();

        assertThat(result.getResult(), is(AAWIFI));
        assertThat(result.getParameter(), is(MERCHANT_ACCT));
        assertThat(result.getProcessor(), is(ADYEN));
    }
}
