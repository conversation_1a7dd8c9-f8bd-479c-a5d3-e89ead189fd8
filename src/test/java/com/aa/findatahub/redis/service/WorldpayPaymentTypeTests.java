package com.aa.findatahub.redis.service;

import com.aa.findatahub.redis.ComponentTest;
import com.aa.findatahub.redis.controller.RedisController;
import com.aa.findatahub.redis.entity.*;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.testcontainers.shaded.com.google.common.collect.HashMultimap;

import java.time.LocalDate;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

@ExtendWith(MockitoExtension.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class WorldpayPaymentTypeTests extends ComponentTest {
    @Autowired
    RedisController controller;

    @Autowired
    @Qualifier("worldpayPaymentType")
    ReactiveRedisTemplate redisTemplate;


    @BeforeAll
    void setup() {
        redisTemplate.opsForZSet().add(new WorldpayPaymentTypeKey("Visa"), new WorldpayPaymentType("VI", "Visa"), 1.0).block();
        redisTemplate.opsForZSet().add(new WorldpayPaymentTypeKey("Visa AndroidPay"), new WorldpayPaymentType("VI", "Visa AndroidPay"), 1.0).block();
        redisTemplate.opsForZSet().add(new WorldpayPaymentTypeKey("Visa Applepay"), new WorldpayPaymentType("VI", "Visa Applepay"), 1.0).block();
        redisTemplate.opsForZSet().add(new WorldpayPaymentTypeKey("MasterCard"), new WorldpayPaymentType("MC", "MasterCard"), 1.0).block();
    }

    @DisplayName("Given a payment method, when the worldpay payment type redis cache is queried, then return the payment type")
    @ParameterizedTest
    @CsvSource({
            "Visa,VI",
            "MasterCard,MC",
            "Visa AndroidPay,VI"
    })
    void testGetWorldpayPaymentType_Success(String method, String type) {
        var result = controller.getWorldpayPaymentType(method).block();
        assertThat(result, is(type));
    }
}
