package com.aa.findatahub.redis.service;

import com.aa.findatahub.redis.ComponentTest;
import com.aa.findatahub.redis.controller.RedisController;
import com.aa.findatahub.redis.entity.ReceivableAccounting;
import com.aa.findatahub.redis.entity.ReceivableAccountingLookup;
import com.aa.findatahub.redis.exception.NoDataFoundException;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

@ExtendWith(value = {MockitoExtension.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class ReceivableAccountingTests extends ComponentTest{

    @InjectMocks
    @Autowired
    RedisController controller;

    @Autowired
    @Qualifier("receivableAccounting")
    ReactiveRedisTemplate receivableAccounting;

    @MockitoSpyBean
    @Autowired
    RedisService redisService;

    @Test
    public void givenRedisCache_WhenReceivableAccountingFound_ThenReturnTrue() {
        ReceivableAccountingLookup lookup = new ReceivableAccountingLookup
                ("AX", "AX", "COMPANY", "SALES", "USD");
        ReceivableAccounting foundValue =  new ReceivableAccounting
                ("AX", "AX", "COMPANY", "SALES", "USD",
                        "2985c1cbe36bfc28e042ae5902469038c28e12a7b2a87e7926f58478cc8b3fec", "AA00", "",
                        "1240019", "1240071", "AX Co Sales Billing");
        Mono<ReceivableAccounting> paymentTypeProducer = controller.getReceivableAccounting(lookup);
        StepVerifier.create(paymentTypeProducer)
                .expectNext(foundValue)
                .verifyComplete();

    }

    @Test
    public void givenRedisCache_WhenReceivableAccountingNotFound_ThenReturnNoDataFound() {
        ReceivableAccountingLookup lookup = new ReceivableAccountingLookup
                ("AX", "AX", "COMPANY", "SALES", "CNY");
        Mono<ReceivableAccounting> paymentTypeProducer = controller.getReceivableAccounting(lookup);
        StepVerifier.create(paymentTypeProducer)
                .expectError(NoDataFoundException.class);

    }


    @BeforeAll
    public void loadCache(){
        ReceivableAccountingLookup lookup = new ReceivableAccountingLookup
                ("AX", "MS", "Company",
                "SALES", "USD");

        ReceivableAccounting value =  new ReceivableAccounting
                ("AX", "MS", "Company", "SALES", "USD",
                "9a4470c7bbec73a9e4fe009cd26d6023d2a0dd913b3528c7631d625ccb9ad789", "AA00", "cost_center",
                "1240019", "1240126", "AMEX GATEWAY BILLING AFOP");
        receivableAccounting.opsForValue().set(lookup, value).block();


        lookup = new ReceivableAccountingLookup
                ("AX", "AX", "COMPANY", "SALES", "USD");

        value =  new ReceivableAccounting
                ("AX", "AX", "COMPANY", "SALES", "USD",
                "2985c1cbe36bfc28e042ae5902469038c28e12a7b2a87e7926f58478cc8b3fec", "AA00", "",
                "1240019", "1240071", "AX Co Sales Billing");
        receivableAccounting.opsForValue().set(lookup, value).block();


    }
}
