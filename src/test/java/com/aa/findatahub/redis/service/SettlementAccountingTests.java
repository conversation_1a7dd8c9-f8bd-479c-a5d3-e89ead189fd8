package com.aa.findatahub.redis.service;

import com.aa.findatahub.redis.ComponentTest;
import com.aa.findatahub.redis.controller.RedisController;
import com.aa.findatahub.redis.entity.SettlementAccounting;
import com.aa.findatahub.redis.entity.SettlementAccountingKey;
import com.aa.findatahub.redis.exception.NoDataFoundException;
import com.aa.findatahub.redis.lookup.SettlementAccountingLookup;
import com.aa.itfacs.pmt.mask.Masked;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.testcontainers.shaded.com.google.common.collect.HashMultimap;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDate;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.samePropertyValuesAs;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

@Slf4j
@ExtendWith(value = {MockitoExtension.class})
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SettlementAccountingTests extends ComponentTest {

    @Autowired
    RedisController redisController;

    @Autowired
    ReactiveRedisTemplate<SettlementAccountingKey, SettlementAccounting> settlementAccounting;

    ObjectMapper mapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

    HashMultimap<SettlementAccountingKey, SettlementAccounting> validEntries = HashMultimap.create();
    HashMultimap<SettlementAccountingKey, SettlementAccounting> duplicateEntries = HashMultimap.create();
    HashMultimap<SettlementAccountingKey, SettlementAccounting> junkEntries = HashMultimap.create();
    HashMultimap<SettlementAccountingKey, ? super Object> invalidEntries = HashMultimap.create();

    @BeforeAll
    void setup() {
        loadCache();
    }

    @Test
    @DisplayName("Given AllParamsRule, when ZSet data pulled from Redis, then verify data found")
    void givenSettlementAccountingLookups_whenAllParamsRuleRun_ThenVerifyReturnedCacheItems() {
        var values = filterForItemsWithNullFieldValues(validEntries.values(), List.of());
        assertFalse(values.isEmpty());

        for(var value : values) {
            SettlementAccountingLookup settlementAccountingLookup = new SettlementAccountingLookup(
                    value.getEffectiveStartDate(),
                    value.getEffectiveEndDate(),
                    value.getProcessor(),
                    value.getMerchant(),
                    value.getPaymentType(),
                    value.getTransactionType(),
                    value.getCurrency());

            assertFoundData(settlementAccountingLookup, value);
        }
    }

    @Test
    @DisplayName("Given NoMerchantRule with null payment type, when ZSet data pulled from Redis, then verify data found")
    void givenSettlementAccountingLookupsMissingMerchantAndPaymentType_whenAllParamsRuleRun_ThenVerifyReturnedCacheItems() {
        var values = filterForItemsWithNullFieldValues(validEntries.values(), List.of("merchant", "paymentType"));
        assertFalse(values.isEmpty());

        for(var value : values) {
            SettlementAccountingLookup settlementAccountingLookup = new SettlementAccountingLookup(
                    value.getEffectiveStartDate(),
                    value.getEffectiveEndDate(),
                    value.getProcessor(),
                    "TestMerchant",
                    value.getPaymentType(),
                    value.getTransactionType(),
                    value.getCurrency());

            assertFoundData(settlementAccountingLookup, value);
        }
    }

    @Test
    @DisplayName("Given NoMerchantRule, when ZSet data pulled from Redis, then verify data found")
    void givenValidSettlementAccountingLookupsMissingMerchant_whenNoMerchantRuleRun_ThenVerifyReturnedCacheItems() {
        var values = filterForItemsWithNullFieldValues(validEntries.values(), List.of("merchant"));
        assertFalse(values.isEmpty());

        for(var value : values) {
            SettlementAccountingLookup settlementAccountingLookup = new SettlementAccountingLookup(
                    value.getEffectiveStartDate(),
                    value.getEffectiveEndDate(),
                    value.getProcessor(),
                    "TestMerchant",
                    value.getPaymentType(),
                    value.getTransactionType(),
                    value.getCurrency());

            assertFoundData(settlementAccountingLookup, value);
        }
    }

    @Test
    @DisplayName("Given multiple lookups, when ZSet data is found in Redis, then verify returned lists are accurate")
    void givenValidSettlementAccountingLookups_whenControllerGetValueMethodCalled_ThenVerifyReturnedCacheItems() throws JsonProcessingException {
        for(var key : validEntries.keySet()) {
            var entries = Optional.of(validEntries.get(key))
                    .orElse(Set.of())
                    .stream()
                    .toList();

            log.info("Searching for SettlementAccounting with key: {}", key.toMaskedString());

            var returnedList = ((Mono<List<SettlementAccounting>>) redisController.getZSets(SettlementAccounting.class.getSimpleName(), key)).block();
            assertNotNull(returnedList);
            assertEquals(returnedList.size(), entries.size());
            assertTrue(returnedList.containsAll(entries));

            log.info("Found list of matching SettlementAccounting: {}", mapper.writeValueAsString(returnedList));
        }
    }

    @Test
    @DisplayName("Given multiple entries for the NoPaymentTypeMerchantTransTypeRule, when ZSet data pulled from Redis with overlapping dates, then return no data found")
    void givenDuplicateSettlementAccountingLookups_whenAllParmasRuleRun_ThenVerifyNoDataReturnedError() {
        assertFalse(duplicateEntries.isEmpty());

        duplicateEntries.values().stream()
                .max(Comparator.comparing(
                        SettlementAccounting::getEffectiveEndDate,
                        Comparator.nullsFirst(Comparator.reverseOrder())))
                .ifPresentOrElse(value -> {
                    SettlementAccountingLookup settlementAccountingLookup = new SettlementAccountingLookup(
                            value.getEffectiveStartDate(),
                            value.getEffectiveEndDate(),
                            value.getProcessor(),
                            value.getMerchant(),
                            value.getPaymentType(),
                            value.getTransactionType(),
                            value.getCurrency());

                    assertException(settlementAccountingLookup, NoDataFoundException.class);
                }, () -> fail("No entry found"));
    }

    @Test
    @DisplayName("Given multiple entries with null required properties, when ZSet data pulled from Redis, then return no data found")
    void givenSettlementAccountingLookup_whenRequiredPropertiesAreNull_ThenVerifyNoDataReturnedError() {
        var values = filterForItemsWithNullFieldValues(validEntries.values(), List.of());
        assertFalse(values.isEmpty());

        for(var value : values) {
            SettlementAccountingLookup settlementAccountingLookup = new SettlementAccountingLookup(
                    null,
                    null,
                    null,
                    value.getMerchant(),
                    value.getPaymentType(),
                    value.getTransactionType(),
                    null);

            assertException(settlementAccountingLookup, NoDataFoundException.class);
        }
    }

    @Test
    @DisplayName("Given valid lookup, when ZSet data pulled from Redis is junk data, then return no data found")
    void givenValidSettlementAccountingLookup_whenRedisDataIsJunk_ThenVerifyNoDataReturnedError() {
        assertFalse(junkEntries.isEmpty());

        for(var values : junkEntries.values()) {
            SettlementAccountingLookup settlementAccountingLookup = new SettlementAccountingLookup(
                    values.getEffectiveStartDate(),
                    values.getEffectiveEndDate(),
                    values.getProcessor(),
                    values.getMerchant(),
                    values.getPaymentType(),
                    values.getTransactionType(),
                    values.getCurrency());

            assertException(settlementAccountingLookup, NoDataFoundException.class);
        }
    }

    @Test
    @DisplayName("Given lookup with invalid data in Redis, when ZSet data pulled from Redis, then return no data found")
    void givenInvalidSettlementAccountingLookup_whenPulledFromRedis_ThenVerifyNoDataReturnedError() {
        SettlementAccountingLookup settlementAccountingLookup = new SettlementAccountingLookup(
                LocalDate.now().minusDays(10),
                LocalDate.now().plusDays(10),
                "INVALID",
                "MerchantName",
                "CreditCard",
                "Sale",
                "USD");

        assertException(settlementAccountingLookup, NoDataFoundException.class);
    }

    @Test
    @DisplayName("Given null settlement accounting lookup, when ZSet data pulled from Redis, then return no data found")
    void givenNullSettlementAccountingLookup_whenPulledFromRedis_ThenVerifyIllegalArgumentError() {
        assertException(null, IllegalArgumentException.class);
    }

    @Test
    @DisplayName("Given settlement accounting lookup with invalid processor, when ZSet data pulled from Redis, then return no data found")
    void givenValidSettlementAccountingLookup_whenInvalidDataPulledFromRedis_ThenVerifySerializationError() {
        assertFalse(invalidEntries.isEmpty());

        for(var value : invalidEntries.values()) {
            SettlementAccounting processedValue = value instanceof SettlementAccounting ? (SettlementAccounting) value : new SettlementAccounting();
            SettlementAccountingLookup settlementAccountingLookup = new SettlementAccountingLookup(
                    processedValue.getEffectiveStartDate(),
                    processedValue.getEffectiveEndDate(),
                    processedValue.getProcessor(),
                    processedValue.getMerchant(),
                    processedValue.getTransactionType(),
                    processedValue.getCurrency(),
                    processedValue.getCurrency());

            assertException(settlementAccountingLookup, NoDataFoundException.class);
        }
    }

    private void loadCache() {
        addToMap(
                validEntries,
                LocalDate.now().minusDays(10),
                LocalDate.now().plusDays(10),
                "ADYEN",
                "MerchantName",
                "CreditCard",
                "Sale",
                "USD",
                "CREDIT_COMP_CODE",
                "CREDIT_ACCOUNT",
                "DEBIT_COMP_CODE",
                "DEBIT_ACCOUNT",
                "COST_CENTER",
                "Line Item Text"
        );

        addToMap(
                validEntries,
                LocalDate.now().minusDays(10),
                LocalDate.now().plusDays(10),
                "ADYEN",
                "MerchantName",
                null,
                "Sale",
                "USD",
                "CREDIT_COMP_CODE",
                "CREDIT_ACCOUNT",
                "DEBIT_COMP_CODE",
                "DEBIT_ACCOUNT",
                "COST_CENTER",
                "Line Item Text"
        );

        addToMap(
                validEntries,
                LocalDate.now().minusDays(10),
                LocalDate.now().plusDays(10),
                "ADYEN",
                null,
                "CreditCard",
                "Sale",
                "USD",
                "CREDIT_COMP_CODE",
                "CREDIT_ACCOUNT",
                "DEBIT_COMP_CODE",
                "DEBIT_ACCOUNT",
                "COST_CENTER",
                "Line Item Text"
        );

        addToMap(
                validEntries,
                LocalDate.now().minusDays(10),
                LocalDate.now().plusDays(10),
                "ADYEN",
                null,
                null,
                "Sale",
                "USD",
                "CREDIT_COMP_CODE",
                "CREDIT_ACCOUNT",
                "DEBIT_COMP_CODE",
                "DEBIT_ACCOUNT",
                "COST_CENTER",
                "Line Item Text"
        );

        addToMap(
                validEntries,
                LocalDate.now().minusDays(15),
                LocalDate.now().plusDays(15),
                "AFFIRM",
                "AnotherMerchant",
                "DebitCard",
                "Refund",
                "EUR",
                "ANOTHER_CREDIT_COMP_CODE",
                "ANOTHER_CREDIT_ACCOUNT",
                "ANOTHER_DEBIT_COMP_CODE",
                "ANOTHER_DEBIT_ACCOUNT",
                "ANOTHER_COST_CENTER",
                "Another Line Item Text"
        );

        addToMap(
                validEntries,
                LocalDate.now().minusDays(15),
                LocalDate.now().plusDays(15),
                "WORLDPAY",
                "",
                null,
                "Refund",
                "EUR",
                "ANOTHER_CREDIT_COMP_CODE",
                "ANOTHER_CREDIT_ACCOUNT",
                "ANOTHER_DEBIT_COMP_CODE",
                "ANOTHER_DEBIT_ACCOUNT",
                "ANOTHER_COST_CENTER",
                "Another Line Item Text"
        );

        addToMap(
                duplicateEntries,
                LocalDate.now().minusDays(20),
                LocalDate.now().plusDays(20),
                "AMEX",
                "MerchantName",
                "CreditCard",
                "Sale",
                "USD",
                "CREDIT_COMP_CODE",
                "CREDIT_ACCOUNT",
                "DEBIT_COMP_CODE",
                "DEBIT_ACCOUNT",
                "COST_CENTER",
                "Line Item Text"
        );

        addToMap(
                duplicateEntries,
                LocalDate.now().minusDays(10),
                LocalDate.now().plusDays(10),
                "AMEX",
                "MerchantName",
                "CreditCard",
                "Sale",
                "USD",
                "CREDIT_COMP_CODE",
                "CREDIT_ACCOUNT",
                "DEBIT_COMP_CODE",
                "DEBIT_ACCOUNT",
                "COST_CENTER",
                "Line Item Text"
        );

        addToMap(
                junkEntries,
                null,
                null,
                "JUNK",
                null,
                null,
                null,
                "USD",
                null,
                null,
                null,
                null,
                null,
                null
        );

        invalidEntries.put(new SettlementAccountingKey("INVALID1", "USD"), "{}");
        invalidEntries.put(new SettlementAccountingKey("INVALID2", "USD"), "{}");
        invalidEntries.put(new SettlementAccountingKey("INVALID2", "USD"), new SettlementAccounting());
    }

    private void addToMap(HashMultimap<SettlementAccountingKey, SettlementAccounting> map, LocalDate effectiveStartDate,
                          LocalDate effectiveEndDate, String processor, String merchant, String paymentType, String transactionType,
                          String currency, String creditCompCode, String creditAccount, String debitCompCode, String debitAccount,
                          String costCenter, String lineItemText) {
        SettlementAccountingKey key = new SettlementAccountingKey(processor, currency);
        SettlementAccounting value = new SettlementAccounting(
                effectiveStartDate,
                effectiveEndDate,
                processor,
                merchant,
                paymentType,
                transactionType,
                currency,
                creditCompCode,
                creditAccount,
                debitCompCode,
                debitAccount,
                costCenter,
                lineItemText
        );
        map.put(key, value);
        settlementAccounting.opsForZSet().add(key, value, 0).block();
    }

    private List<SettlementAccounting> filterForItemsWithNullFieldValues(Collection<SettlementAccounting> values, List<String> nullFieldNames) {
        return values.stream()
                .filter(value -> {
                    Map<String, Object> fieldMap = mapper.convertValue(value, new TypeReference<>() {});

                    for (String fieldName : fieldMap.keySet()) {
                        Object fieldValue = fieldMap.get(fieldName);

                        if ((fieldValue == null || (fieldValue instanceof String && StringUtils.isEmpty((String) fieldValue)))
                                != nullFieldNames.contains(fieldName)) {
                            return false;
                        }
                    }
                    return true;
                })
                .toList();
    }

    private void assertFoundData(SettlementAccountingLookup settlementAccountingLookup, SettlementAccounting value) {
        try {
            log.info("Searching for SettlementAccounting with lookup: {}", mapper.writeValueAsString(settlementAccountingLookup));

            SettlementAccounting result = redisController.getSettlementAccounting(settlementAccountingLookup).block();
            assertNotNull(result);
            assertThat(result, samePropertyValuesAs(value));

            log.info("Found matching SettlementAccounting: {}", mapper.writeValueAsString(result));
        } catch (JsonProcessingException e) {
            fail(e.getMessage());
        }
    }

    private void assertException(SettlementAccountingLookup settlementAccountingLookup, Class<? extends Exception> exception) {
        try {
            log.info("Trigger exception {} for SettlementAccounting with lookup: {}", exception.getSimpleName(), mapper.writeValueAsString(settlementAccountingLookup));

            StepVerifier.create(redisController.getSettlementAccounting(settlementAccountingLookup))
                    .expectError(exception)
                    .verify();

            log.info("Error thrown: {}", exception.getSimpleName());
        } catch (JsonProcessingException e) {
            fail(e.getMessage());
        }
    }
}