package com.aa.findatahub.redis;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.wait.strategy.Wait;
import org.testcontainers.utility.DockerImageName;

import com.redis.testcontainers.RedisContainer;

@SpringBootTest
public abstract class ComponentTest {
    static DockerImageName REDIS_TEST_IMAGE = DockerImageName.parse("redis:6.2.6");
    static RedisContainer redisContainer = new RedisContainer(REDIS_TEST_IMAGE)
            .waitingFor(Wait.forListeningPort());

    static {
        redisContainer.start();
    }

    @DynamicPropertySource
    static void setProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.data.redis.host", redisContainer::getHost);
        registry.add("spring.data.redis.port", () -> redisContainer.getFirstMappedPort());
        registry.add("spring.data.redis.password", () -> "");
        registry.add("spring.data.redis.ssl.enabled", () -> false);
    }
}

