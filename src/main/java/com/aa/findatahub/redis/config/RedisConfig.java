package com.aa.findatahub.redis.config;

import io.lettuce.core.ClientOptions;
import io.lettuce.core.SslOptions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ServiceLocatorFactoryBean;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;

import com.aa.findatahub.redis.service.ReactiveRedisTemplateFactory;

import javax.net.ssl.SNIHostName;
import javax.net.ssl.SSLParameters;
import java.io.File;
import java.io.FileWriter;
import java.util.Collections;
import java.util.UUID;

@Configuration
public class RedisConfig {

    private static final Logger logger = LoggerFactory.getLogger(RedisConfig.class);

    @Bean("lookupServiceFactory")
    public FactoryBean<Object> serviceLocatorFactoryBean() {
        ServiceLocatorFactoryBean factoryBean = new ServiceLocatorFactoryBean();
        factoryBean.setServiceLocatorInterface(ReactiveRedisTemplateFactory.class);
        return factoryBean;
    }

    @Bean(name = "reactiveRedisConnectionFactory")
    public ReactiveRedisConnectionFactory redisConnectionFactory(
            RedisProperties redisProperties,
            @Value("${spring.ssl.bundle.pem.mybundle.truststore.certificate}") String certFileString
    ) throws Exception {

        File certFile = File.createTempFile("redis-cert-" + UUID.randomUUID(), ".pem");
        try (FileWriter writer = new FileWriter(certFile)) {
            writer.write(certFileString);
        }

        RedisStandaloneConfiguration redisConfig = new RedisStandaloneConfiguration();
        redisConfig.setHostName(redisProperties.getHost());
        redisConfig.setPort(redisProperties.getPort());
        redisConfig.setUsername(redisProperties.getUsername());
        redisConfig.setPassword(redisProperties.getPassword());

        LettuceClientConfiguration.LettuceClientConfigurationBuilder lettuceClientConfigurationBuilder = LettuceClientConfiguration.builder();

        if (certFileString != null && !certFileString.isEmpty()) {
            SSLParameters sslParameters = new SSLParameters();
            sslParameters.setServerNames(Collections.singletonList(new SNIHostName(redisProperties.getHost())));

            ClientOptions clientOptions = ClientOptions.builder()
                    .sslOptions(SslOptions.builder()
                            .jdkSslProvider()
                            .trustManager(certFile)
                            .build())
                    .build();

            lettuceClientConfigurationBuilder
                    .clientOptions(clientOptions)
                    .useSsl()
                    .disablePeerVerification();
        }

        LettuceClientConfiguration lettuceClientConfiguration = lettuceClientConfigurationBuilder.build();
        LettuceConnectionFactory factory = new LettuceConnectionFactory(redisConfig, lettuceClientConfiguration);

        factory.afterPropertiesSet();

        try {
            factory.getConnection().ping();
            logger.info("Successfully connected to Redis");
        } catch (Exception e) {
            logger.error("Failed to connect to Redis", e);
        }

        return factory;
    }

}
