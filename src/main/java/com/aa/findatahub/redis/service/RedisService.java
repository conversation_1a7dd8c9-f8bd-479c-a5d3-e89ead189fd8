package com.aa.findatahub.redis.service;

import java.time.Duration;
import java.util.List;
import java.util.function.Supplier;

import com.aa.findatahub.redis.entity.PrefixedRedisKey;
import com.aa.findatahub.redis.entity.RedisKey;
import com.aa.findatahub.redis.rule.Rule;
import com.aa.findatahub.redis.rule.RuleExecutor;
import com.aa.findatahub.redis.utils.Util;

import lombok.RequiredArgsConstructor;
import lombok.extern.java.Log;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Range;
import org.springframework.data.redis.RedisConnectionFailureException;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.aa.findatahub.redis.exception.NoDataFoundException;

import reactor.core.Exceptions;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;
import reactor.util.retry.RetryBackoffSpec;

import static com.aa.findatahub.redis.utils.AppConstants.LOG_ON_FETCH;
import static com.aa.findatahub.redis.utils.AppConstants.LOG_ON_EMPTY;
import static com.aa.findatahub.redis.utils.AppConstants.LOG_ON_EMPTY_WITH_LATENCY;


@Service
@RequiredArgsConstructor
public class RedisService {

    Logger log = LoggerFactory.getLogger(RedisService.class);

    @Value("${retry.times}")
    private int retryTime;

    @Value("${retry.delay}")
    private int delay;

    private final ReactiveRedisTemplateFactory templateFactory;
    private final RuleExecutor ruleExecutor;

    @SuppressWarnings("unchecked")
    public Mono<?> getValue(String channelName, Object key) {

        long startTime = System.currentTimeMillis();
        log.debug(LOG_ON_FETCH, Util.toJson(key));
        RetryBackoffSpec spec = Retry.backoff(retryTime, Duration.ofSeconds(delay)).filter((t -> t instanceof RedisConnectionFailureException));
        return Mono.fromCallable(() -> templateFactory.getRedisTemplate(StringUtils.uncapitalize(channelName)).opsForValue().get(key))
                .flatMap(m -> m)
                .retryWhen(spec)
                .onErrorMap(e -> {
                    if (e instanceof SerializationException) {
                        return new SerializationException("Cache data invalid");
                    } else if (Exceptions.isRetryExhausted((Throwable)e)) {
                        return new RedisConnectionFailureException("Error connecting to redis");
                    }
                    return e;
                })
                .switchIfEmpty(Mono.error(createNoDataFoundErrorSupplier(key, startTime)));
    }

    @SuppressWarnings("unchecked")
    public Mono<?> getZSets(String channelName, Object key) {
        if (key == null) {
            return Mono.error(() -> new IllegalArgumentException("Key cannot be null"));
        }

        long startTime = System.currentTimeMillis();
        log.debug(LOG_ON_FETCH, Util.toJson(key));

        return Mono.fromCallable(()-> templateFactory.getRedisTemplate(StringUtils.uncapitalize(channelName)).opsForZSet()
                        .range(key, Range.from(Range.Bound.inclusive(0L)).to(Range.Bound.inclusive(-1L))))
                .flatMapMany(flux -> flux)
                .collectList()
                .filter(l -> !((List<?>) l).isEmpty())
                .retryWhen(Retry.backoff(retryTime, Duration.ofSeconds(delay)))
                .onErrorMap(RuntimeException.class, _ ->
                        new RedisConnectionFailureException("Error Connecting to Cache"))
                .switchIfEmpty(Mono.error(createNoDataFoundErrorSupplier(key, startTime)));
    }

    public <V, K> Flux<V> getZSets(Class<V> valueType, K key, RedisKey keyObj) {
        if (valueType == null) {
            return Flux.error(() -> new IllegalArgumentException("Value type cannot be null"));
        }
        if (key == null) {
            return Flux.error(() -> new IllegalArgumentException("Key cannot be null"));
        }

        long startTime = System.currentTimeMillis();
        log.debug(LOG_ON_FETCH, Util.toJson(key));

        return templateFactory.<K, V>getTypedRedisTemplate(StringUtils.uncapitalize(valueType.getSimpleName()))
                .opsForZSet()
                .range(key, Range.from(Range.Bound.inclusive(0L)).to(Range.Bound.inclusive(-1L)))
                .retryWhen(Retry.backoff(retryTime, Duration.ofSeconds(delay)))
                .onErrorMap(RuntimeException.class, _ ->
                        new RedisConnectionFailureException("Error Connecting to Cache"))
                .switchIfEmpty(Flux.error(createNoDataFoundErrorSupplier(keyObj, startTime)));
    }

    public <V> Mono<V> getFirstValueByRules(Class<V> valueType, RedisKey key, @NonNull List<Rule<V>> rules, boolean throwForDuplicates) {
        Flux<V> response = key instanceof PrefixedRedisKey
                ? getZSets(valueType, key.toString(), key)
                : getZSets(valueType, key, key);
        return ruleExecutor.applyRules(rules, response, throwForDuplicates)
                .switchIfEmpty(Mono.error(() -> new NoDataFoundException(key)));
    }

    private <K> Supplier<NoDataFoundException> createNoDataFoundErrorSupplier(K key, long startTime) {
        return () -> {
            log.info(LOG_ON_EMPTY, Util.toJson(key));
            log.debug(LOG_ON_EMPTY_WITH_LATENCY, Util.toJson(key), System.currentTimeMillis() - startTime);
            return new NoDataFoundException(key);
        };
    }

}
