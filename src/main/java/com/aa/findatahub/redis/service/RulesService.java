package com.aa.findatahub.redis.service;

import com.aa.findatahub.redis.entity.LocalBillingStation;
import com.aa.findatahub.redis.entity.SettlementAccounting;
import com.aa.findatahub.redis.lookup.LocalBillingStationLookup;
import com.aa.findatahub.redis.lookup.SettlementAccountingLookup;
import com.aa.findatahub.redis.rule.Rule;
import com.aa.findatahub.redis.rule.localbillingstation.DateRule;
import com.aa.findatahub.redis.rule.settlementaccounting.AllParamsRule;
import com.aa.findatahub.redis.rule.settlementaccounting.NoMerchantRule;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@NoArgsConstructor
public class RulesService {
    public List<Rule<SettlementAccounting>> getSettlementAccountingRules(SettlementAccountingLookup settlementAccountingLookup) {
        return new ArrayList<>(List.of(
                new AllParamsRule(settlementAccountingLookup),
                new NoMerchantRule(settlementAccountingLookup)
        ));
    }

    public List<Rule<LocalBillingStation>> getLocalBillingStationRules(LocalBillingStationLookup localBillingStationLookup) {
        return new ArrayList<>(List.of(
                new DateRule(localBillingStationLookup)
        ));
    }
}
