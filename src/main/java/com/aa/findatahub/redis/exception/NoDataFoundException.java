package com.aa.findatahub.redis.exception;

import com.aa.findatahub.redis.entity.RedisKey;
import org.springframework.lang.NonNull;

public class NoDataFoundException extends RedisInterfaceException {
    public <K> NoDataFoundException(@NonNull final K key) {
        super("No data found", key);
    }

    public NoDataFoundException(@NonNull final RedisKey key) {
        super("No data found", key);
    }
}
