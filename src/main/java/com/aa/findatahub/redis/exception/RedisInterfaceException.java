package com.aa.findatahub.redis.exception;

import com.aa.findatahub.redis.entity.RedisKey;
import com.aa.itfacs.pmt.mask.Masked;
import org.springframework.lang.NonNull;

public abstract class RedisInterfaceException extends RuntimeException {
    public <K> RedisInterfaceException(@NonNull String message, @NonNull final K key) {
        super(String.format("%s in Redis for %s: %s", message.trim(), key.getClass().getSimpleName(), Masked.objectToMaskedString(key)));
    }

    public RedisInterfaceException(@NonNull String message, @NonNull final RedisKey key) {
        super(String.format("%s in Redis for %s: %s", message.trim(), key.getClass().getSimpleName(), key.toMaskedString()));
    }
}
