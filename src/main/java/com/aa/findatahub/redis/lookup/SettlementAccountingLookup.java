package com.aa.findatahub.redis.lookup;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;

@Getter
@AllArgsConstructor
public class SettlementAccountingLookup {
    private LocalDate effectiveStartDate;
    private LocalDate effectiveEndDate;
    private String processor;
    private String merchant;
    private String paymentType;
    private String transactionType;
    private String currency;
}
