package com.aa.findatahub.redis.lookup;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.time.LocalDate;

@Getter
@AllArgsConstructor
public class LocalBillingStationLookup {
    private String transactionType;
    private String stationNumber;
    private String paymentType;
    private String countryCode;
    private String currency;
    private LocalDate effectiveStartDate;
    private LocalDate effectiveEndDate;
}
