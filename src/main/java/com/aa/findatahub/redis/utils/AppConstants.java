package com.aa.findatahub.redis.utils;

import lombok.NoArgsConstructor;

@NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
public class AppConstants {
    public static final String LOCAL_BILLING_STATION_KEY_PREFIX = "aa-ct-ccrecon-payment-localbillingstation";

    public static final String LOG_ON_FETCH = "Fetching value for key: {}";
    public static final String LOG_ON_EMPTY = "No data found for key: {}";
    public static final String LOG_ON_EMPTY_WITH_LATENCY = "No data found for key: {}, latency: {} ms";
}
