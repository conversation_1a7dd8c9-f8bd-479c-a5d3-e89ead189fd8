package com.aa.findatahub.redis.utils;

import com.aa.findatahub.redis.entity.RedisKey;
import com.aa.itfacs.pmt.mask.Masked;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

public class Util {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static String substring(String string,int startIndex,int endIndex){
        return string.substring(startIndex, Math.min(endIndex, string.length()));
    }

    public static String toJson(Object obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            return "{}";
        }
    }

    public static String toMaskedJsonWithOriginalOrder(RedisKey redisKey) {
        if (redisKey == null) {
            return "{}";
        }

        String json = toJson(redisKey);
        String maskedJson = Masked.objectToMaskedString(redisKey, true);

        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(json);
            JsonNode maskedNode = OBJECT_MAPPER.readTree(maskedJson);
            ObjectNode resultNode = OBJECT_MAPPER.createObjectNode();

            jsonNode.fieldNames().forEachRemaining(field -> {
                if (maskedNode.has(field)) {
                    resultNode.set(field, maskedNode.get(field));
                }
            });

            return OBJECT_MAPPER.writeValueAsString(resultNode);
        } catch (JsonProcessingException e) {
            return maskedJson;
        }
    }
}
