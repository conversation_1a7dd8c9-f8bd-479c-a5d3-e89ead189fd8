package com.aa.findatahub.redis.mapper;

import com.aa.findatahub.redis.entity.LocalBillingStationKey;
import com.aa.findatahub.redis.lookup.LocalBillingStationLookup;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.time.LocalDate;
import java.util.Optional;

@Mapper(componentModel = "spring")
public interface LocalBillingStationMapper {
    LocalBillingStationKey toKey(LocalBillingStationLookup lookup);

    @Mapping(target = "paymentType", source = "paymentType")
    @Mapping(target = "transactionType", source = "transactionType")
    @Mapping(target = "stationNumber", source = "station")
    @Mapping(target = "countryCode", source = "country")
    @Mapping(target = "currency", source = "currency")
    @Mapping(target = "effectiveStartDate", source = "issueDate", qualifiedByName = "toLocalDate")
    @Mapping(target = "effectiveEndDate", source = "issueDate", qualifiedByName = "toLocalDate")
    LocalBillingStationLookup toLookup(final String paymentType, final String transactionType, final String station,
                                       final String country, final String currency, final CharSequence issueDate);

    @Named("toLocalDate")
    default LocalDate toLocalDate(CharSequence issueDate) {
        return Optional.ofNullable(issueDate)
                .map(CharSequence::toString)
                .map(LocalDate::parse)
                .orElse(null);
    }
}
