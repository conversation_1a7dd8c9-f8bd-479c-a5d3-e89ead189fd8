package com.aa.findatahub.redis.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ProcessorKey extends RedisKey {

	private String paymentType;
	private String countryCode;
	private String currency;
	private String salesChannel;
	private String transactionType;

}
