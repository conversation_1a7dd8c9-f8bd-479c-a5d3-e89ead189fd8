package com.aa.findatahub.redis.entity;

import org.springframework.stereotype.Component;

import com.aa.findatahub.redis.annotation.ReactiveRedisTemplateBean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
@ReactiveRedisTemplateBean(jsonKeySerializerType = ProcessorKey.class)
public class Processor {

	private String payment_type;
	private String country_code;
	private String currency;
	private String sales_channel;
	private String transaction_type;
	private String processor_id;
	private String merchant_id;
	private String accounting_reference_indicator;

}
