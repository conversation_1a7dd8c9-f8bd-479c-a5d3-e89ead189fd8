package com.aa.findatahub.redis.entity;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import static com.aa.findatahub.redis.utils.AppConstants.LOCAL_BILLING_STATION_KEY_PREFIX;

@Getter
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class LocalBillingStationKey extends PrefixedRedisKey {
    private String transactionType;
    private String stationNumber;
    private String paymentType;
    private String countryCode;
    private String currency;

    @Override
    public String getPrefix() {
        return LOCAL_BILLING_STATION_KEY_PREFIX;
    }
}
