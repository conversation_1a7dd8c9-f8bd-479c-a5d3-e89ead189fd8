package com.aa.findatahub.redis.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import com.aa.findatahub.redis.annotation.ReactiveRedisTemplateBean;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
@ReactiveRedisTemplateBean(jsonKeySerializerType = PaymentTypeLookup.class)
public class PaymentType {
    private String binPrefix;
    private String lastFourDigits;
    private String paymentType;
    private String description;
    private Integer authCancelMandate;
    private Integer authCancelAAPolicy;
    private boolean amadeusafop;

}
