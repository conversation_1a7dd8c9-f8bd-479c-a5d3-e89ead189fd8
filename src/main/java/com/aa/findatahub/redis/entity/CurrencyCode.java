package com.aa.findatahub.redis.entity;


import com.aa.findatahub.redis.annotation.ReactiveRedisTemplateBean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

@Data
@Component
@AllArgsConstructor
@NoArgsConstructor
@ReactiveRedisTemplateBean(jsonKeySerializerType = CurrencyKey.class)
public class CurrencyCode {
    Integer decimalPlaces;
    String currencyCode;
    String countryCode;
}
