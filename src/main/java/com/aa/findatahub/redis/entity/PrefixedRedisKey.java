package com.aa.findatahub.redis.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.micrometer.common.util.StringUtils;
import lombok.Getter;

import java.util.Optional;

@Getter
public abstract class PrefixedRedisKey extends RedisKey {
    @JsonIgnore
    protected abstract String getPrefix();

    @Override
    public String toString() {
        String json = super.toString();
        return withPrefix(json);
    }

    @Override
    public String toMaskedString() {
        String maskedString = super.toMaskedString();
        return withPrefix(maskedString);
    }

    private String withPrefix(String json) {
        return Optional.ofNullable(getPrefix())
                .filter(StringUtils::isNotBlank)
                .map(String::trim)
                .map(p -> p + ":" + json)
                .orElse(json);
    }
}
