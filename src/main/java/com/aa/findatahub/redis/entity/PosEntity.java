package com.aa.findatahub.redis.entity;

import org.springframework.stereotype.Component;

import com.aa.findatahub.redis.annotation.ReactiveRedisTemplateBean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Component
@ReactiveRedisTemplateBean
public class PosEntity {
    private String stationNum;
    private String city;
    private String state;
    private String zip;
    private String alphaCountryCode;
    private String countryCode;
    private String alphaCurrency;
    private String numericCurrency;
    private int decimalPosition;
    private String salesChannel;
    private String region; 
    private String usdConvertIndicator;
    private String aaaType;
    private String province;
}
