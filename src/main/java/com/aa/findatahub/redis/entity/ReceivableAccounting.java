package com.aa.findatahub.redis.entity;

import lombok.*;
import org.springframework.stereotype.Component;

import com.aa.findatahub.redis.annotation.ReactiveRedisTemplateBean;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
@ReactiveRedisTemplateBean(jsonKeySerializerType = ReceivableAccountingLookup.class)
public class ReceivableAccounting {

        public String payment_type;
        public String processor;
        public String sales_channel;
        public String transaction_type;
        public String currency;

        public String reference_id;
        public String company_code;
        public String cost_center;
        public String debit_account;
        public String credit_account;
        public String line_item_text;

}
