package com.aa.findatahub.redis.entity;

import com.aa.findatahub.redis.utils.Util;
import lombok.Getter;

public abstract class RedisKey {
    @Getter
    private final String className = getClass().getSimpleName();

    @Override
    public String toString() {
        return Util.toJson(this);
    }

    public String toMaskedString() {
        return Util.toMaskedJsonWithOriginalOrder(this);
    }
}
