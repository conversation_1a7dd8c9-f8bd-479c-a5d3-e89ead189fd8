package com.aa.findatahub.redis.entity;

import com.aa.findatahub.redis.annotation.ReactiveRedisTemplateBean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

@Data
@Component
@NoArgsConstructor
@AllArgsConstructor
@ReactiveRedisTemplateBean(jsonKeySerializerType = AriReceivableAccountingKey.class)
public class AriReceivableAccounting {
    String accountingReferenceIndicator;
    String debitAccount;
    String creditAccount;
    String creditCompanyCode;
    String debitCompanyCode;
    String costCenter;
    String lineItemText;
}
