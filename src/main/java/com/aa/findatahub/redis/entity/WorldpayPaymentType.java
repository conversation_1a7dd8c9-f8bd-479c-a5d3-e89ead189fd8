package com.aa.findatahub.redis.entity;

import com.aa.findatahub.redis.annotation.ReactiveRedisTemplateBean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
@ReactiveRedisTemplateBean(jsonKeySerializerType = WorldpayPaymentTypeKey.class)
public class WorldpayPaymentType
{
    public String paymentType;
    public String paymentMethod;
}
