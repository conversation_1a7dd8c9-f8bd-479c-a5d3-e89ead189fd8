package com.aa.findatahub.redis.entity;

import java.time.LocalDate;
import com.aa.findatahub.redis.annotation.ReactiveRedisTemplateBean;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
@ReactiveRedisTemplateBean
public class LocalBillingStation {

    @JsonProperty("transaction_type")
    private String transactionType;

    @JsonProperty("station_number")
    private String stationNumber;

    @JsonProperty("payment_type")
    private String paymentType;

    @JsonProperty("country_code")
    private String countryCode;

    private String currency;

    private boolean cps;

    private boolean vivaldi;

    @JsonProperty("processor_id")
    private String processorId;

    @JsonProperty("merchant_id")
    private String merchantId;

    @JsonProperty("accounting_reference_indicator")
    private String accountingReferenceIndicator;

    @JsonProperty("effective_start_date")
    private LocalDate effectiveStartDate;

    @JsonProperty("effective_end_date")
    private LocalDate effectiveEndDate;

}