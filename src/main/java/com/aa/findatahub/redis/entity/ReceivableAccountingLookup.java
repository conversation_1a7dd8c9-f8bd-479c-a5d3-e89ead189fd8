package com.aa.findatahub.redis.entity;


import lombok.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ReceivableAccountingLookup {

        //Payment Type, Processor, Sales Channel, Transaction Type & Currency Code
        public String payment_type;
        public String processor;
        public String sales_channel;
        public String transaction_type;
        public String currency;
}
