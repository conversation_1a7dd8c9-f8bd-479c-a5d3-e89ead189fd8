package com.aa.findatahub.redis.entity;

import com.aa.findatahub.redis.annotation.ReactiveRedisTemplateBean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Component
@ReactiveRedisTemplateBean(jsonKeySerializerType = NonSabreTicketKey.class)
public class NonSabreTicket {
    private String processor;
    private String parameter;
    private String result;
}
