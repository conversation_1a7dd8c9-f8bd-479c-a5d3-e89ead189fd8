package com.aa.findatahub.redis.entity;

import com.aa.findatahub.redis.annotation.ReactiveRedisTemplateBean;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
@ReactiveRedisTemplateBean(jsonKeySerializerType = SettlementAccountingKey.class)
public class SettlementAccounting {
    private LocalDate effectiveStartDate;
    private LocalDate effectiveEndDate;
    private String processor;
    private String merchant;
    private String paymentType;
    private String transactionType;
    private String currency;
    private String creditCompCode;
    private String creditAccount;
    private String debitCompCode;
    private String debitAccount;
    private String costCenter;
    private String lineItemText;
}
