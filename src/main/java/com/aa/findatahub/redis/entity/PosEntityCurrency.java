package com.aa.findatahub.redis.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import com.aa.findatahub.redis.annotation.ReactiveRedisTemplateBean;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Component
@ReactiveRedisTemplateBean
public class PosEntityCurrency {
    private String countryCode;
    private String alphaCurrency;
    private String numericCurrency;
}
