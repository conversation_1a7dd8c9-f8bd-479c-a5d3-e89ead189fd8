package com.aa.findatahub.redis.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.Import;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Import(ReactiveRedisTemplateBeanRegistrar.class)
public @interface ReactiveRedisTemplateBean  {

    Class<? extends RedisSerializer<?>> keySerializer() default StringRedisSerializer.class;
    Class<?> jsonKeySerializerType() default Void.class;
}
