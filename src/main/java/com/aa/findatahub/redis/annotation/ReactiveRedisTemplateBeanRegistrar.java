package com.aa.findatahub.redis.annotation;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

import lombok.SneakyThrows;

@Component

public class ReactiveRedisTemplateBeanRegistrar implements BeanPostProcessor {

    @Autowired
    @Qualifier("reactiveRedisConnectionFactory")
    private ReactiveRedisConnectionFactory factory;

    @Autowired
    ObjectMapper mapper;


    @Override
    public Object postProcessAfterInitialization(@NonNull Object bean, @NonNull String beanName) throws BeansException {
        return bean;
    }

    @SneakyThrows 
    @Override
    public Object postProcessBeforeInitialization(Object bean, @NonNull String beanName) throws BeansException {
        if (bean.getClass().isAnnotationPresent(ReactiveRedisTemplateBean.class)) {
            ReactiveRedisTemplateBean annotationBean = bean.getClass().getAnnotation(ReactiveRedisTemplateBean.class);
            if (annotationBean.jsonKeySerializerType() != Void.class) {
                return typeTemplate(factory, bean.getClass(), new Jackson2JsonRedisSerializer<>(mapper, annotationBean.jsonKeySerializerType()));
            } else {
                Class<? extends RedisSerializer<?>> inputSerlClass = annotationBean.keySerializer();
                return typeTemplate(factory, bean.getClass(), inputSerlClass.getConstructor().newInstance());
            }
        }

        return bean;
    }

    private ReactiveRedisTemplate<?, ?> typeTemplate(
            ReactiveRedisConnectionFactory factory, Class<?> myClass, RedisSerializer<?> keySerializer)
    {
        Jackson2JsonRedisSerializer valueSerializer = new Jackson2JsonRedisSerializer<>(mapper, myClass);
        RedisSerializationContext.RedisSerializationContextBuilder builder = RedisSerializationContext.newSerializationContext(keySerializer);
        RedisSerializationContext context = builder.value(valueSerializer).build();
        return new ReactiveRedisTemplate(factory, context);
    }

}
