package com.aa.findatahub.redis.controller;

import com.aa.findatahub.redis.entity.*;
import com.aa.findatahub.redis.lookup.LocalBillingStationLookup;
import com.aa.findatahub.redis.exception.NoDataFoundException;
import com.aa.findatahub.redis.lookup.SettlementAccountingLookup;
import com.aa.findatahub.redis.mapper.LocalBillingStationMapper;
import com.aa.findatahub.redis.rule.Rule;
import com.aa.findatahub.redis.service.RedisService;
import com.aa.findatahub.redis.service.RulesService;
import com.aa.findatahub.redis.utils.Util;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.security.InvalidParameterException;
import java.util.List;

@Component
@RequiredArgsConstructor
public class RedisController {

    private final RedisService redisService;
    private final RulesService rulesService;
    private final LocalBillingStationMapper localBillingStationMapper;

    public Mono<?> getValue(String tableName, Object key) {
        return redisService.getValue(tableName, key);
    }

    public Mono<?> getZSets(String tableName, Object key) {
        return redisService.getZSets(tableName, key);
    }

    public Mono<List<PosEntityCurrency>> getPosEntityCurrencies(String countryCode) {
        return (Mono<List<PosEntityCurrency>>) redisService.getZSets("PosEntityCurrency", countryCode);
    }

    public Mono<Integer> getMinorUnit(String currencyCode) {
        return redisService.getValue("PaymentCurrencyUnit", currencyCode)
                .cast(PaymentCurrencyUnit.class)
                .map(PaymentCurrencyUnit::getMinorUnits);
    }

    public Mono<String> getProcessorCode(String processorName) {
        return redisService.getValue("ProcessorCode", processorName)
                .cast(ProcessorCode.class)
                .map(ProcessorCode::getProcessorCode);
    }


    public Mono<String> getWorldpayPaymentType(String paymentMethod) {
        WorldpayPaymentTypeKey key = new WorldpayPaymentTypeKey(paymentMethod);
        return redisService.getZSets(WorldpayPaymentType.class, key, key)
                .cast(WorldpayPaymentType.class)
                .map(WorldpayPaymentType::getPaymentType)
                .next();
    }

    public Mono<CurrencyCode> getCurrencyCode(CurrencyKey currencyKey) {
        return redisService.getValue("CurrencyCode", currencyKey)
                .cast(CurrencyCode.class);
    }

    /***
     *
     * @param bin
     * @param lastFour
     * @return
     */
    public Mono<PaymentType> getPaymentType(String bin, String lastFour) {
        if (bin == null) {
            return Mono.error(InvalidParameterException::new);
        }
        bin = Util.substring(bin, 0, 4);
        if (!bin.contains("X") &&Integer.valueOf(bin) < 2000 && Integer.valueOf(bin) > 1000) {
            lastFour = null;
        } else {
            bin = Util.substring(bin, 0, 2);
        }
        if (lastFour != null) {
            lastFour = lastFour.toLowerCase();
        }
        PaymentTypeLookup lookup = new PaymentTypeLookup(bin, lastFour);
        String finalBin = bin;
        String finalLastFour = lastFour;
        return redisService.getValue("paymentType", lookup)
                .cast(PaymentType.class)
                .onErrorResume(NoDataFoundException.class, e -> {
                    if (finalLastFour != null && finalLastFour.length() > 1) {
                        return getPaymentType(finalBin, finalLastFour.substring(1));
                    }
                    return getPaymentType(finalBin);

                });
    }

    public Mono<PaymentType> getPaymentType(String bin) {
        PaymentTypeLookup lookup = new PaymentTypeLookup(bin, "");
        return redisService.getValue("paymentType", lookup)
                .cast(PaymentType.class)
                .onErrorResume(NoDataFoundException.class, e -> {
                    if (bin.length() > 1) {
                        return getPaymentType(bin.substring(0, bin.length() - 1));
                    }
                    return Mono.error(e);
                });

    }

    public Mono<LocalBillingStation> getLocalBillingStation(LocalBillingStationLookup lookup) {
        if (lookup == null) {
            return Mono.error(new IllegalArgumentException(LocalBillingStationLookup.class.getSimpleName() + " cannot be null"));
        }

        LocalBillingStationKey localBillingStationKey = localBillingStationMapper.toKey(lookup);
        List<Rule<LocalBillingStation>> rules = rulesService.getLocalBillingStationRules(lookup);

        return redisService.getFirstValueByRules(LocalBillingStation.class, localBillingStationKey, rules, true);
    }

    public Mono<LocalBillingStation> getLocalBillingStation(final String paymentType, final String transactionType, final String station,
            final String country, final String currency, final CharSequence issueDate) {
        LocalBillingStationLookup lookup = localBillingStationMapper.toLookup(paymentType, transactionType, station, country, currency, issueDate);
        return getLocalBillingStation(lookup);
    }

    /***
     *
     * @param lookup
     * @return
     */
    public Mono<ReceivableAccounting> getReceivableAccounting(ReceivableAccountingLookup lookup) {
        Mono<ReceivableAccounting> retval = null;
        retval = redisService.getValue("receivableAccounting", lookup).cast(ReceivableAccounting.class);

        return retval;
    }

    public Mono<Processor> getProcessor(ProcessorKey lookup) {
        return redisService.getValue("processor", lookup).cast(Processor.class);
    }

    public Mono<AriReceivableAccounting> getAccountingReference(AriReceivableAccountingKey ariReceivableAccountingKey) {
        return redisService.getValue(AriReceivableAccounting.class.getSimpleName(), ariReceivableAccountingKey).cast(AriReceivableAccounting.class);
    }

    public Mono<NonSabreTicket> getNonSabreTicket(String processor, String result) {
        return redisService.getValue(NonSabreTicket.class.getSimpleName(), new NonSabreTicketKey(processor.toUpperCase(), result.toUpperCase()))
                .cast(NonSabreTicket.class);
    }

    public Mono<SettlementAccounting> getSettlementAccounting(SettlementAccountingLookup lookup) {
        if (lookup == null) {
            return Mono.error(new IllegalArgumentException(SettlementAccountingLookup.class.getSimpleName() + " cannot be null"));
        }

        SettlementAccountingKey settlementAccountingKey = new SettlementAccountingKey(lookup.getProcessor(), lookup.getCurrency());
        List<Rule<SettlementAccounting>> rules = rulesService.getSettlementAccountingRules(lookup);

        return redisService.getFirstValueByRules(SettlementAccounting.class, settlementAccountingKey, rules, false);
    }
}
