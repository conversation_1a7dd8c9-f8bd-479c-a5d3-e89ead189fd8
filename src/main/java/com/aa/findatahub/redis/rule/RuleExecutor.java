package com.aa.findatahub.redis.rule;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class RuleExecutor {

    public <V> Mono<V> applyRules(List<Rule<V>> rules, Flux<V> values, boolean throwForDuplicates) {
        if (isEmpty(rules)) {
            log.warn("No rules provided");
            return Mono.empty();
        }

        // Cache the values so each rule sees the same stream
        Flux<V> cachedValues = values.cache();

        return Flux.fromIterable(rules)
                .concatMap(rule -> rule.applyRule(cachedValues, throwForDuplicates).flux())
                .next()
                .switchIfEmpty(Mono.fromRunnable(() -> log.warn("No single value found that matches rules")))
                .doOnError(e -> log.error("Error applying rules: {}", e.getClass().getSimpleName()));
    }

    private boolean isEmpty(List<?> list) {
        return Optional.ofNullable(list)
                .map(List::isEmpty)
                .orElse(true);
    }
}
