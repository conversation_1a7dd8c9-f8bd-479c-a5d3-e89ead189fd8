package com.aa.findatahub.redis.rule.settlementaccounting;

import com.aa.findatahub.redis.entity.SettlementAccounting;
import com.aa.findatahub.redis.lookup.SettlementAccountingLookup;
import com.aa.findatahub.redis.rule.AbstractRule;

import java.util.Objects;
import java.util.function.Predicate;

public class AllParamsRule extends AbstractRule<SettlementAccountingLookup, SettlementAccounting> {
    public AllParamsRule(SettlementAccountingLookup lookup) {
        super(lookup);
    }

    @Override
    protected Predicate<SettlementAccounting> getPredicate() {
        return item -> isAfterDate(lookup.getEffectiveStartDate(), item.getEffectiveStartDate()) &&
                isBeforeDate(lookup.getEffectiveEndDate(), item.getEffectiveEndDate()) &&
                Objects.equals(item.getProcessor(), lookup.getProcessor()) &&
                Objects.equals(item.getPaymentType(), lookup.getPaymentType()) &&
                Objects.equals(item.getMerchant(), lookup.getMerchant()) &&
                Objects.equals(item.getTransactionType(), lookup.getTransactionType()) &&
                Objects.equals(item.getCurrency(), lookup.getCurrency());
    }
}
