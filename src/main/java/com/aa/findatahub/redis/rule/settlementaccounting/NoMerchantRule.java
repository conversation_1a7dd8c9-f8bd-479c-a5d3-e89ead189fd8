package com.aa.findatahub.redis.rule.settlementaccounting;

import com.aa.findatahub.redis.entity.SettlementAccounting;
import com.aa.findatahub.redis.lookup.SettlementAccountingLookup;
import com.aa.findatahub.redis.rule.AbstractRule;
import io.micrometer.common.util.StringUtils;

import java.util.Objects;
import java.util.function.Predicate;

public class NoMerchantRule extends AbstractRule<SettlementAccountingLookup, SettlementAccounting> {

    public NoMerchantRule(SettlementAccountingLookup lookup) {
        super(lookup);
    }

    @Override
    protected Predicate<SettlementAccounting> getPredicate() {
        return item -> isAfterDate(lookup.getEffectiveStartDate(), item.getEffectiveStartDate()) &&
                isBeforeDate(lookup.getEffectiveEndDate(), item.getEffectiveEndDate()) &&
                Objects.equals(item.getProcessor(), lookup.getProcessor()) &&
                Objects.equals(item.getPaymentType(), lookup.getPaymentType()) &&
                StringUtils.isEmpty(item.getMerchant()) &&
                Objects.equals(item.getTransactionType(), lookup.getTransactionType()) &&
                Objects.equals(item.getCurrency(), lookup.getCurrency());
    }

}
