package com.aa.findatahub.redis.rule;

import com.aa.findatahub.redis.exception.DuplicateFoundException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Predicate;

public abstract class AbstractRule<K, V> implements Rule<V> {
    protected final K lookup;
    protected final Logger log = LoggerFactory.getLogger(this.getClass());
    protected final String className = this.getClass().getSimpleName();
    protected final ObjectMapper mapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

    protected AbstractRule(@NonNull K lookup) {
        this.lookup = lookup;
    }

    protected Predicate<V> getPredicate() {
        return _ -> true; // Default implementation
    }

    public Mono<V> applyRule(Flux<V> values, boolean throwForDuplicates) {
        logRuleExecutionWithLookup();
        Predicate<V> predicate = getPredicate();
        AtomicInteger count = new AtomicInteger(0);

        return values
                .<V>handle((v, sink) -> {
                    if (predicate.test(v)) {
                        int current = count.incrementAndGet();
                        sink.next(v);
                        if (current >= 2) {
                            sink.complete();
                        }
                    }
                })
                .take(2)
                .collectList()
                .flatMap(filteredValues -> switch (filteredValues.size()) {
                    case 1 -> {
                        log.info("Found exactly one value that applies to rule {}", className);
                        yield Mono.just(filteredValues.getFirst());
                    }
                    case 0 -> {
                        log.warn("No value found that applies to rule {}", className);
                        yield Mono.empty();
                    }
                    default -> {
                        log.warn("Found more than one value that applies to rule {}", className);
                        yield throwForDuplicates
                                ? Mono.error(new DuplicateFoundException(lookup))
                                : Mono.empty();
                    }
                });
    }

    private void logRuleExecutionWithLookup() {
        String lookupClassName = lookup.getClass().getSimpleName();
        try {
            log.info("Running {} with {}: {}", className, lookupClassName, mapper.writeValueAsString(lookup));
        } catch (JsonProcessingException e) {
            log.error("Running {} with {}. Error serializing lookup: {}", className, lookupClassName, e.getMessage());
        }
    }

    protected boolean isAfterDate(LocalDate lookup, LocalDate comparison) {
        return Optional.ofNullable(lookup)
                .map(date -> date.isAfter(comparison) || date.isEqual(comparison))
                .orElse(false);
    }

    protected boolean isBeforeDate(LocalDate lookup, LocalDate comparison) {
        return Optional.ofNullable(lookup)
                .map(date -> date.isBefore(comparison) || date.isEqual(comparison))
                .orElse(false);
    }
}
