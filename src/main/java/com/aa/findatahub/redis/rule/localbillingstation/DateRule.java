package com.aa.findatahub.redis.rule.localbillingstation;

import com.aa.findatahub.redis.entity.LocalBillingStation;
import com.aa.findatahub.redis.lookup.LocalBillingStationLookup;
import com.aa.findatahub.redis.rule.AbstractRule;

import java.util.function.Predicate;

public class DateRule extends AbstractRule<LocalBillingStationLookup, LocalBillingStation> {

    public DateRule(LocalBillingStationLookup lookup) {
        super(lookup);
    }

    @Override
    protected Predicate<LocalBillingStation> getPredicate() {
        return item -> isAfterDate(lookup.getEffectiveStartDate(), item.getEffectiveStartDate()) &&
                isBeforeDate(lookup.getEffectiveEndDate(), item.getEffectiveEndDate());
    }
}