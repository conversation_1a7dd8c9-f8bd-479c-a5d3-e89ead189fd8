spring:
  data:
    redis:
      host:
      username:
      password:
      port:
  ssl:
    bundle:
      pem:
        mybundle:
          truststore:
            certificate:
    enabled:

retry:
  delay: 5
  times: 2

management:
  endpoint:
    chaosmonkey:
      enabled: true
    chaosmonkeyjmx:
      enabled: true

  endpoints:
    web:
      exposure:
        include:
          - chaosmonkey

