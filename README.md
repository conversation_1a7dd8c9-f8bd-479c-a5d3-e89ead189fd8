# itfacs-findatahub-cache-interface

Reusable interface to interact with ITFACS FinDataHub Redis cache. Used for fast access to static reference data maintained in the FinDataHub Curated Database.
## Architecture Diagram

<img width="708" alt="Diagram - 5 x Redis Cache 09062023" src="https://github.com/AAInternal/itfacs-findatahub-redis-interface/assets/114776863/acf07562-67dd-411e-8fe1-1ed2241415c9">


## Azure Resources

| Resource              | Resource Name | Region |
| :-------------------: | :-----------: | :----: |
| Redis Cache for Azure | TBD           | East   |